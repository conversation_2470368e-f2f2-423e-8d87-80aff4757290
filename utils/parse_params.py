from urllib.parse import unquote
doc = '''
orgid=5a8bcd47-8199-4161-8757-e625361d881f&type=gns&__RequestVerificationToken=ruwFW3zbVhRBpB4J5Z_bvfKUIlbqDqy7be6BHEZGps5Pg2qiPyg_j4UraX9PyESp3UoXf2B7dxD-l6g941oYQmabIvLydQtw2uUxgGwYGto1&__hiddenToken=jbqWy78JMBcXAat7LJzieGr858J7zhNMXCjM0a8Ab3WpuwbABIn2kRGXimslvfNVle2G8n8EBJ4=
'''
data = {}
for item in doc.split('&'):
    tmp = item.split('=', 1)
    print(f'{tmp[0]}:{unquote(tmp[1])}')