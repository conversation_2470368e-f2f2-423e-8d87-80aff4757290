#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化打包脚本 - 快速生成可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
from datetime import datetime

def main():
    """简化的打包流程"""
    print("🚀 安全教育平台 - 简化打包工具")
    print("=" * 50)
    
    project_root = Path(__file__).parent
    
    # 检查必要文件
    required_files = ['start_web.py', 'index.py', 'app.py']
    missing_files = [f for f in required_files if not (project_root / f).exists()]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    # 安装PyInstaller
    print("📦 检查PyInstaller...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller>=5.0'], 
                      check=True, capture_output=True)
        print("✅ PyInstaller准备就绪")
    except subprocess.CalledProcessError:
        print("❌ PyInstaller安装失败")
        return False
    
    # 清理旧文件
    print("🧹 清理旧文件...")
    for dir_name in ['build', 'dist', '__pycache__']:
        dir_path = project_root / dir_name
        if dir_path.exists():
            shutil.rmtree(dir_path)
    
    # 构建Web版本
    print("🌐 构建Web版本...")
    web_cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',
        '--console',  # 改为显示控制台，方便调试
        '--name=安全教育平台_Web版',
        '--add-data=templates;templates',
        '--add-data=config.ini.example;.',
        '--hidden-import=flask',
        '--hidden-import=flask_socketio',
        '--hidden-import=socketio',
        '--hidden-import=engineio',
        '--hidden-import=loguru',
        '--hidden-import=requests',
        '--hidden-import=bs4',
        '--hidden-import=beautifulsoup4',
        '--hidden-import=lxml',
        '--hidden-import=sqlite3',
        '--hidden-import=configparser',
        '--hidden-import=threading',
        '--hidden-import=json',
        '--hidden-import=datetime',
        '--hidden-import=database',
        '--hidden-import=app',
        '--hidden-import=index',
        'start_web.py'
    ]
    
    try:
        subprocess.run(web_cmd, check=True, cwd=project_root)
        print("✅ Web版本构建完成")
    except subprocess.CalledProcessError:
        print("❌ Web版本构建失败")
        return False
    
    # 构建命令行版本
    print("⚡ 构建命令行版本...")
    cli_cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',
        '--console',
        '--name=安全教育平台_命令行版',
        '--add-data=config.ini.example;.',
        '--hidden-import=loguru',
        '--hidden-import=requests',
        '--hidden-import=bs4',
        '--hidden-import=beautifulsoup4',
        '--hidden-import=lxml',
        '--hidden-import=sqlite3',
        '--hidden-import=configparser',
        '--hidden-import=database',
        'index.py'
    ]
    
    try:
        subprocess.run(cli_cmd, check=True, cwd=project_root)
        print("✅ 命令行版本构建完成")
    except subprocess.CalledProcessError:
        print("❌ 命令行版本构建失败")
        return False
    
    # 创建发布目录
    print("📁 创建发布包...")
    dist_dir = project_root / "简化打包结果"
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    dist_dir.mkdir()
    
    # 复制可执行文件
    exe_files = list((project_root / "dist").glob("*.exe"))
    for exe_file in exe_files:
        shutil.copy2(exe_file, dist_dir)
    
    # 复制配置文件
    config_file = project_root / "config.ini.example"
    if config_file.exists():
        shutil.copy2(config_file, dist_dir)
    
    # 创建简单启动脚本
    startup_script = '''@echo off
chcp 65001 >nul
title 安全教育平台

echo 安全教育平台自动化工具
echo ========================
echo.
echo [1] 启动Web界面
echo [2] 启动命令行版本
echo [0] 退出
echo.
set /p choice=请选择: 

if "%choice%"=="1" (
    echo 启动Web界面...
    "安全教育平台_Web版.exe"
) else if "%choice%"=="2" (
    echo 启动命令行版本...
    "安全教育平台_命令行版.exe"
) else (
    exit
)

pause
'''
    
    with open(dist_dir / "启动.bat", 'w', encoding='gbk') as f:
        f.write(startup_script)
    
    # 创建说明文件
    readme = '''# 安全教育平台 - 简化版

## 使用方法

1. 双击 `启动.bat`
2. 选择启动方式
3. 首次使用请先配置 `config.ini.example`

## 文件说明

- `安全教育平台_Web版.exe` - Web界面版本
- `安全教育平台_命令行版.exe` - 命令行版本
- `config.ini.example` - 配置文件模板
- `启动.bat` - 启动脚本

## 配置说明

将 `config.ini.example` 重命名为 `config.ini` 并填写：

```
[Credentials]
username = 你的用户名
password = 你的密码
role = admin
```
'''
    
    with open(dist_dir / "README.txt", 'w', encoding='utf-8') as f:
        f.write(readme)
    
    print("=" * 50)
    print("🎉 简化打包完成！")
    print(f"📁 文件位置: {dist_dir}")
    print("📋 包含文件:")
    for file in dist_dir.iterdir():
        print(f"   - {file.name}")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        input("按回车键退出...")
    else:
        input("打包失败，按回车键退出...")
