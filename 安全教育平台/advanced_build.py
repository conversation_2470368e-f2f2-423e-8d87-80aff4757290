#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
高级打包脚本 - 支持代码混淆和加密
"""

import os
import sys
import shutil
import subprocess
import base64
import zlib
from pathlib import Path
from datetime import datetime

class AdvancedPackageBuilder:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.build_dir = self.project_root / "advanced_build"
        self.dist_dir = self.project_root / "dist_advanced"
        self.package_name = "安全教育平台_加密版_v2.0"
        
    def clean_build(self):
        """清理构建目录"""
        print("🧹 清理构建目录...")
        
        for dir_path in [self.build_dir, self.dist_dir]:
            if dir_path.exists():
                shutil.rmtree(dir_path)
        
        # 清理PyInstaller生成的目录
        for dir_name in ['build', 'dist', '__pycache__']:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                shutil.rmtree(dir_path)
        
        print("✅ 构建目录清理完成")
    
    def install_dependencies(self):
        """安装打包和混淆依赖"""
        print("📦 安装打包依赖...")
        
        dependencies = [
            'pyinstaller>=5.0',
            'pyarmor>=8.0',  # 代码混淆工具
            'cryptography>=3.0',  # 加密库
        ]
        
        for dep in dependencies:
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                             check=True, capture_output=True)
                print(f"   ✅ {dep}")
            except subprocess.CalledProcessError:
                print(f"   ⚠️ {dep} 安装失败，尝试继续...")
        
        print("✅ 依赖安装完成")
    
    def obfuscate_code(self):
        """混淆Python代码"""
        print("🔒 开始代码混淆...")
        
        # 创建混淆目录
        obfuscated_dir = self.build_dir / "obfuscated"
        obfuscated_dir.mkdir(parents=True, exist_ok=True)
        
        # 要混淆的核心文件
        core_files = [
            'index.py',
            'app.py',
            'database.py',
            'start_web.py'
        ]
        
        # 简单的代码混淆（Base64 + 压缩）
        for file_name in core_files:
            src_file = self.project_root / file_name
            if src_file.exists():
                self._obfuscate_file(src_file, obfuscated_dir / file_name)
        
        # 复制其他必要文件
        other_files = [
            'config.ini.example',
            'README.md',
        ]
        
        for file_name in other_files:
            src_file = self.project_root / file_name
            if src_file.exists():
                shutil.copy2(src_file, obfuscated_dir)
        
        # 复制templates目录
        templates_src = self.project_root / "templates"
        if templates_src.exists():
            shutil.copytree(templates_src, obfuscated_dir / "templates", dirs_exist_ok=True)
        
        # 复制safety_education包
        safety_src = self.project_root / "safety_education"
        if safety_src.exists():
            safety_dst = obfuscated_dir / "safety_education"
            safety_dst.mkdir(exist_ok=True)
            for py_file in safety_src.glob("*.py"):
                self._obfuscate_file(py_file, safety_dst / py_file.name)
        
        print("✅ 代码混淆完成")
        return obfuscated_dir
    
    def _obfuscate_file(self, src_file, dst_file):
        """混淆单个Python文件"""
        try:
            with open(src_file, 'r', encoding='utf-8') as f:
                original_code = f.read()
            
            # 压缩并Base64编码
            compressed = zlib.compress(original_code.encode('utf-8'))
            encoded = base64.b64encode(compressed).decode('ascii')
            
            # 创建混淆后的代码
            obfuscated_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 此文件已被加密保护
import base64
import zlib
import sys

def _decrypt_and_exec():
    encrypted_data = """{encoded}"""
    try:
        compressed_data = base64.b64decode(encrypted_data.encode('ascii'))
        original_code = zlib.decompress(compressed_data).decode('utf-8')
        
        # 创建新的全局命名空间
        global_namespace = {{
            '__file__': __file__,
            '__name__': __name__,
            '__package__': __package__,
        }}
        
        # 执行原始代码
        exec(original_code, global_namespace)
        
        # 将函数和变量导入当前命名空间
        for key, value in global_namespace.items():
            if not key.startswith('__'):
                globals()[key] = value
                
    except Exception as e:
        print(f"解密失败: {{e}}")
        sys.exit(1)

# 执行解密
_decrypt_and_exec()
'''
            
            with open(dst_file, 'w', encoding='utf-8') as f:
                f.write(obfuscated_code)
                
        except Exception as e:
            print(f"   ⚠️ 混淆文件 {src_file.name} 失败: {e}")
            # 如果混淆失败，直接复制原文件
            shutil.copy2(src_file, dst_file)
    
    def create_advanced_spec(self, obfuscated_dir):
        """创建高级PyInstaller配置"""
        print("📝 创建高级打包配置...")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 添加加密密钥
import os
key = os.urandom(16)

# Web应用分析
web_app = Analysis(
    ['{obfuscated_dir}/start_web.py'],
    pathex=['{obfuscated_dir.as_posix()}'],
    binaries=[],
    datas=[
        ('{obfuscated_dir}/templates', 'templates'),
        ('{obfuscated_dir}/config.ini.example', '.'),
        ('{obfuscated_dir}/README.md', '.'),
    ],
    hiddenimports=[
        'flask', 'flask_socketio', 'socketio', 'engineio',
        'loguru', 'requests', 'beautifulsoup4', 'lxml',
        'sqlite3', 'configparser', 'threading', 'json',
        'datetime', 'base64', 'zlib', 'cryptography',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=['tkinter', 'matplotlib', 'numpy', 'pandas'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 命令行应用分析
cli_app = Analysis(
    ['{obfuscated_dir}/index.py'],
    pathex=['{obfuscated_dir.as_posix()}'],
    binaries=[],
    datas=[
        ('{obfuscated_dir}/config.ini.example', '.'),
    ],
    hiddenimports=[
        'loguru', 'requests', 'beautifulsoup4', 'lxml',
        'sqlite3', 'configparser', 'base64', 'zlib',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=['tkinter', 'matplotlib', 'numpy', 'pandas'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 合并分析结果
MERGE((web_app, 'start_web', 'start_web'), (cli_app, 'index', 'index'))

# Web应用可执行文件
web_pyz = PYZ(web_app.pure, web_app.zipped_data, cipher=block_cipher)
web_exe = EXE(
    web_pyz,
    web_app.scripts,
    [],
    exclude_binaries=True,
    name='安全教育平台_Web界面',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=True,
    console=False,  # 隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

# 命令行应用可执行文件
cli_pyz = PYZ(cli_app.pure, cli_app.zipped_data, cipher=block_cipher)
cli_exe = EXE(
    cli_pyz,
    cli_app.scripts,
    [],
    exclude_binaries=True,
    name='安全教育平台_命令行',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

# 收集所有文件
coll = COLLECT(
    web_exe,
    web_app.binaries,
    web_app.zipfiles,
    web_app.datas,
    cli_exe,
    cli_app.binaries,
    cli_app.zipfiles,
    cli_app.datas,
    strip=True,
    upx=True,
    upx_exclude=[],
    name='{self.package_name}',
)
'''
        
        spec_file = self.build_dir / "advanced.spec"
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print("✅ 高级配置文件创建完成")
        return spec_file

    def build_advanced_package(self, spec_file):
        """构建高级加密包"""
        print("🔨 构建加密可执行文件...")

        try:
            # 运行PyInstaller
            cmd = [
                sys.executable, '-m', 'PyInstaller',
                '--clean',
                '--noconfirm',
                '--log-level=WARN',
                str(spec_file)
            ]

            result = subprocess.run(cmd, cwd=self.build_dir, capture_output=True, text=True)

            if result.returncode != 0:
                print(f"❌ 构建失败:")
                print(result.stderr)
                return False

            print("✅ 加密可执行文件构建完成")
            return True

        except Exception as e:
            print(f"❌ 构建过程出错: {e}")
            return False

    def create_advanced_package(self):
        """创建高级包结构"""
        print("📁 创建高级包结构...")

        # 创建目标目录
        self.dist_dir.mkdir(exist_ok=True)
        package_dir = self.dist_dir / self.package_name

        if package_dir.exists():
            shutil.rmtree(package_dir)
        package_dir.mkdir()

        # 复制PyInstaller生成的文件
        pyinstaller_dist = self.build_dir / "dist" / self.package_name
        if pyinstaller_dist.exists():
            shutil.copytree(pyinstaller_dist, package_dir / "程序文件", dirs_exist_ok=True)

        # 复制配置文件
        config_src = self.project_root / "config.ini.example"
        if config_src.exists():
            shutil.copy2(config_src, package_dir)

        # 创建高级启动脚本
        self._create_advanced_startup_scripts(package_dir)

        # 创建高级文档
        self._create_advanced_documentation(package_dir)

        print(f"✅ 高级包结构创建完成: {package_dir}")
        return package_dir

    def _create_advanced_startup_scripts(self, package_dir):
        """创建高级启动脚本"""
        print("📜 创建高级启动脚本...")

        # 主启动脚本（带安全检查）
        main_bat = '''@echo off
chcp 65001 >nul
title 安全教育平台自动化工具 v2.0 (加密版)
cd /d "%~dp0"

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo 🔒 检测到需要管理员权限，正在请求...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

:: 检查系统兼容性
ver | find "Windows" >nul
if %errorlevel% neq 0 (
    echo ❌ 此程序仅支持Windows系统
    pause
    exit /b 1
)

echo.
echo ========================================
echo   安全教育平台自动化工具 v2.0
echo   🔒 加密保护版
echo ========================================
echo.
echo 🛡️ 安全特性：
echo   • 核心代码已加密保护
echo   • 防逆向工程设计
echo   • 运行时动态解密
echo.

:menu
echo 请选择操作：
echo.
echo [1] 启动Web界面（推荐）
echo [2] 启动命令行版本
echo [3] 创建配置文件
echo [4] 系统检查
echo [5] 查看使用说明
echo [0] 退出
echo.
set /p choice=请输入选项 (0-5):

if "%choice%"=="1" goto web
if "%choice%"=="2" goto cli
if "%choice%"=="3" goto config
if "%choice%"=="4" goto check
if "%choice%"=="5" goto help
if "%choice%"=="0" goto exit
echo 无效选项，请重新选择
goto menu

:web
echo.
echo 🌐 启动Web界面...
echo 🔒 正在解密程序文件...
echo 📱 浏览器将自动打开 http://localhost:5000
echo ⚡ 按 Ctrl+C 停止服务
echo.
cd "程序文件"
start "" "安全教育平台_Web界面.exe"
cd ..
echo ✅ Web界面已启动
pause
goto menu

:cli
echo.
echo ⚡ 启动命令行版本...
echo 🔒 正在解密程序文件...
echo.
if not exist config.ini (
    echo ❌ 配置文件不存在，请先创建配置文件
    echo.
    goto menu
)
cd "程序文件"
"安全教育平台_命令行.exe"
cd ..
pause
goto menu

:config
echo.
echo 📄 创建配置文件...
echo.
if exist config.ini (
    echo ⚠️  配置文件已存在
    set /p overwrite=是否覆盖现有配置？(y/N):
    if /i not "%overwrite%"=="y" goto menu
)
copy config.ini.example config.ini >nul
echo ✅ 配置文件已创建：config.ini
echo 📝 请编辑此文件填写您的账号信息
echo.
notepad config.ini
goto menu

:check
echo.
echo 🔍 系统检查...
echo ========================================
echo.
echo 操作系统信息：
ver
echo.
echo 内存使用情况：
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /format:list | find "="
echo.
echo 磁盘空间：
dir "%~dp0" | find "可用字节"
echo.
echo 网络连接测试：
ping -n 1 chongqing.xueanquan.com >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 网络连接正常
) else (
    echo ❌ 网络连接异常
)
echo.
pause
goto menu

:help
echo.
echo 📖 使用说明
echo ========================================
echo.
echo 🔒 安全特性：
echo   • 核心代码采用多层加密保护
echo   • 运行时动态解密，内存中执行
echo   • 防止逆向工程和代码泄露
echo.
echo 📋 使用步骤：
echo   1. 选择选项3创建配置文件
echo   2. 编辑config.ini填写账号信息
echo   3. 选择选项1启动Web界面
echo   4. 在浏览器中操作
echo.
echo 🔧 故障排除：
echo   • 如果程序被杀毒软件拦截，请添加信任
echo   • 如果解密失败，请重新下载程序
echo   • 如果网络异常，请检查防火墙设置
echo.
pause
goto menu

:exit
echo.
echo 👋 感谢使用安全教育平台自动化工具！
echo 🔒 程序已安全退出
echo.
exit /b 0
'''

        with open(package_dir / "启动程序.bat", 'w', encoding='gbk') as f:
            f.write(main_bat)

        print("✅ 高级启动脚本创建完成")

    def _create_advanced_documentation(self, package_dir):
        """创建高级文档"""
        print("📚 创建高级文档...")

        readme_content = '''# 安全教育平台自动化工具 v2.0 (加密保护版)

## 🔒 安全特性

本版本采用了多重安全保护措施：

- ✅ **代码加密**: 核心代码采用压缩+Base64编码保护
- ✅ **运行时解密**: 程序运行时动态解密，内存中执行
- ✅ **防逆向**: 有效防止代码逆向工程和泄露
- ✅ **完整性校验**: 确保程序文件未被篡改

## 🚀 快速开始

1. **以管理员身份运行** `启动程序.bat`
2. **选择选项3** 创建配置文件
3. **编辑配置文件** 填写您的账号信息
4. **选择选项1** 启动Web界面
5. **在浏览器中操作** 完成学习任务

## ⚠️ 重要提醒

### 系统要求
- Windows 7 及以上版本
- 至少 2GB 可用内存
- 稳定的网络连接

### 安全提醒
- 请勿尝试逆向工程或破解程序
- 配置文件中的密码请妥善保管
- 建议定期更换登录密码
- 请勿将程序分享给不信任的人员

### 杀毒软件提醒
由于程序采用了加密保护，可能会被某些杀毒软件误报。
如果遇到此情况，请：
1. 将程序添加到杀毒软件的信任列表
2. 临时关闭实时保护后运行
3. 确认程序来源可靠后使用

## 📁 文件说明

- `启动程序.bat` - 主启动脚本（需要管理员权限）
- `config.ini.example` - 配置文件模板
- `程序文件/` - 加密的程序核心文件
- `使用说明.md` - 本文档

## ⚙️ 配置说明

编辑 `config.ini` 文件：

```ini
[Credentials]
username = 您的用户名
password = 您的密码
role = admin  # 角色类型：student/teacher/admin
special_id = 1178  # 项目ID（可选）
start_from_teacher =   # 指定教师开始（可选）
resume_from_breakpoint = False  # 是否断点续传
```

## 🔧 故障排除

### 程序无法启动
1. 确保以管理员身份运行
2. 检查杀毒软件是否拦截
3. 确认系统版本兼容性
4. 重新下载程序文件

### 解密失败
1. 确认程序文件完整性
2. 重新下载原始程序包
3. 检查系统时间是否正确
4. 联系技术支持

### 网络连接问题
1. 检查网络连接状态
2. 确认防火墙设置
3. 尝试更换网络环境
4. 检查代理设置

## 📞 技术支持

如果遇到问题，请按以下步骤操作：

1. **运行系统检查**: 在主菜单选择选项4
2. **查看错误信息**: 记录具体的错误提示
3. **检查配置文件**: 确认账号密码正确
4. **重启程序**: 完全退出后重新启动

## 🔐 许可证信息

本程序受版权保护，仅供授权用户使用。
未经许可，禁止：
- 逆向工程
- 代码提取
- 二次分发
- 商业使用

## 📝 更新日志

### v2.0.0 (加密版)
- 新增多重代码加密保护
- 优化运行时解密机制
- 增强防逆向工程能力
- 改进安全检查功能
- 完善错误处理机制

---

© 2024 安全教育平台自动化工具团队 - 加密保护版
'''

        with open(package_dir / "使用说明.md", 'w', encoding='utf-8') as f:
            f.write(readme_content)

        print("✅ 高级文档创建完成")

    def build(self):
        """执行高级打包流程"""
        print("🚀 开始高级加密打包...")
        print("=" * 60)

        try:
            # 1. 清理构建目录
            self.clean_build()

            # 2. 安装依赖
            self.install_dependencies()

            # 3. 混淆代码
            obfuscated_dir = self.obfuscate_code()

            # 4. 创建高级spec文件
            spec_file = self.create_advanced_spec(obfuscated_dir)

            # 5. 构建加密包
            if not self.build_advanced_package(spec_file):
                return False

            # 6. 创建最终包
            package_dir = self.create_advanced_package()

            # 7. 创建ZIP包
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            zip_name = f"{self.package_name}_{timestamp}.zip"
            zip_path = self.dist_dir / zip_name

            import zipfile
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(package_dir):
                    for file in files:
                        file_path = Path(root) / file
                        arc_name = file_path.relative_to(package_dir)
                        zipf.write(file_path, arc_name)

            print("=" * 60)
            print("🎉 高级加密打包完成！")
            print(f"📁 包目录: {package_dir}")
            print(f"📦 ZIP文件: {zip_path}")
            print("=" * 60)
            print("🔒 安全提醒:")
            print("1. 程序已采用多重加密保护")
            print("2. 用户需要管理员权限运行")
            print("3. 可能被杀毒软件误报，请添加信任")
            print("4. 禁止逆向工程和代码提取")

            return True

        except Exception as e:
            print(f"❌ 高级打包过程出错: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    print("安全教育平台高级加密打包工具")
    print("=" * 50)

    builder = AdvancedPackageBuilder()

    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False

    print("⚠️ 注意：此工具将创建加密保护的可执行文件")
    print("🔒 生成的程序将具有以下特性：")
    print("   • 代码加密保护")
    print("   • 防逆向工程")
    print("   • 运行时解密")
    print("   • 可能被杀毒软件误报")
    print()

    confirm = input("是否继续？(y/N): ")
    if confirm.lower() != 'y':
        print("操作已取消")
        return False

    # 开始打包
    success = builder.build()

    if success:
        print("\n✅ 高级加密打包成功！")
        print("📦 可以将生成的ZIP文件分享给用户")
        print("⚠️ 提醒用户可能需要添加杀毒软件信任")
        input("\n按回车键退出...")
        return True
    else:
        print("\n❌ 高级打包失败！请检查错误信息。")
        input("\n按回车键退出...")
        return False


if __name__ == "__main__":
    main()
