#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
安全教育平台自动化工具 - 项目初始化脚本

这个脚本用于初始化项目环境，检查依赖，创建必要的文件和目录。
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误：需要Python 3.8或更高版本")
        print(f"   当前版本：{sys.version}")
        return False
    print(f"✅ Python版本检查通过：{sys.version}")
    return True


def check_pdm_installed():
    """检查PDM是否已安装"""
    try:
        result = subprocess.run(['pdm', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ PDM已安装：{result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ PDM未安装")
        return False


def install_pdm():
    """安装PDM"""
    print("📦 正在安装PDM...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pdm'], 
                      check=True)
        print("✅ PDM安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PDM安装失败：{e}")
        return False


def create_directories():
    """创建必要的目录"""
    directories = [
        'safety_education',
        'templates',
        'tests',
        'logs'
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            dir_path.mkdir(parents=True)
            print(f"📁 已创建目录：{directory}")
        else:
            print(f"✅ 目录已存在：{directory}")


def create_config_example():
    """创建配置文件示例"""
    config_example = Path('config.ini.example')
    if not config_example.exists():
        from safety_education.config import Config
        Config.create_example()
        print("📄 已创建配置文件示例：config.ini.example")
    else:
        print("✅ 配置文件示例已存在")


def install_dependencies():
    """安装项目依赖"""
    print("📦 正在安装项目依赖...")
    try:
        subprocess.run(['pdm', 'install'], check=True)
        print("✅ 依赖安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败：{e}")
        print("💡 尝试使用pip安装...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                          check=True)
            print("✅ 使用pip安装依赖成功")
            return True
        except subprocess.CalledProcessError as e2:
            print(f"❌ pip安装也失败：{e2}")
            return False


def setup_git_hooks():
    """设置Git钩子（如果使用Git）"""
    if Path('.git').exists():
        print("🔧 设置Git钩子...")
        try:
            subprocess.run(['pdm', 'run', 'pre-commit', 'install'], 
                          check=True, capture_output=True)
            print("✅ Git钩子设置成功")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  Git钩子设置跳过（pre-commit未安装）")


def show_usage_info():
    """显示使用说明"""
    print("\n" + "="*60)
    print("🎉 项目初始化完成！")
    print("="*60)
    print("\n📚 使用方法：")
    print("\n1. 配置账号信息：")
    print("   cp config.ini.example config.ini")
    print("   # 然后编辑 config.ini 文件")
    print("\n2. 启动Web界面：")
    print("   pdm run web")
    print("   # 或者：python start_web.py")
    print("\n3. 启动命令行版本：")
    print("   pdm run start")
    print("   # 或者：python index.py")
    print("\n4. 其他PDM命令：")
    print("   pdm run test      # 运行测试")
    print("   pdm run format    # 代码格式化")
    print("   pdm run lint      # 代码检查")
    print("\n🌐 Web界面将在 http://localhost:5000 启动")
    print("\n📖 更多信息请查看 README.md")
    print("="*60)


def main():
    """主函数"""
    print("🚀 安全教育平台自动化工具 - 项目初始化")
    print("="*60)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查并安装PDM
    if not check_pdm_installed():
        if not install_pdm():
            print("\n💡 您也可以手动安装PDM：")
            print("   pip install pdm")
            print("\n或者直接使用pip安装依赖：")
            print("   pip install -r requirements.txt")
            sys.exit(1)
    
    # 创建目录结构
    create_directories()
    
    # 创建配置文件示例
    try:
        create_config_example()
    except ImportError:
        print("⚠️  跳过配置文件创建（需要先安装依赖）")
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 依赖安装失败，请手动安装：")
        print("   pdm install")
        print("   # 或者：pip install -r requirements.txt")
        sys.exit(1)
    
    # 设置Git钩子
    setup_git_hooks()
    
    # 显示使用说明
    show_usage_info()


if __name__ == '__main__':
    main()
