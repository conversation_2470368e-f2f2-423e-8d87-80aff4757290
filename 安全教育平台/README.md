# 安全教育平台自动化工具

这是一个功能强大的安全教育平台自动化工具，提供命令行和Web界面两种使用方式，支持学生、教师和管理员三种模式的自动学习。

## 🌟 功能特点

- ✅ **Web可视化界面**: 美观易用的Web界面，实时显示执行进度
- ✅ **命令行支持**: 传统命令行模式，适合自动化部署
- ✅ **PDM包管理**: 使用现代化的PDM进行依赖管理
- ✅ **类型安全**: 使用类型注解，提高代码可读性和维护性
- ✅ **错误处理**: 完善的异常处理机制，提高脚本稳定性
- ✅ **模块化设计**: 清晰的包结构和方法分离
- ✅ **配置文件**: 支持配置文件，方便使用
- ✅ **实时日志**: 详细的日志输出和实时监控
- ✅ **重试机制**: 网络请求失败自动重试
- ✅ **多角色支持**: 支持学生、教师、管理员三种角色

## 📦 安装依赖

### 使用PDM（推荐）

```bash
# 安装PDM（如果还没有安装）
pip install pdm

# 安装项目依赖
pdm install

# 安装开发依赖（可选）
pdm install -G dev
```

### 使用pip

```bash
pip install -r requirements.txt
```

## 🚀 使用方法

### 方式一：Web界面（推荐）

#### 使用PDM启动
```bash
pdm run web
```

#### 或者直接运行
```bash
python start_web.py
```

然后在浏览器中访问 `http://localhost:5000`

### 方式二：命令行模式

#### 1. 配置文件设置

```bash
# 复制配置模板
cp config.ini.example config.ini

# 编辑配置文件
nano config.ini
```

配置文件格式：
```ini
[Credentials]
username = 您的用户名
password = 您的密码
role = teacher  # 选择角色: admin/teacher/student
```

#### 2. 运行脚本

使用PDM：
```bash
pdm run start
```

或者直接运行：
```bash
python index.py
```

## 🎯 角色说明

### 学生模式 (student)
- 自动完成当前学生的所有课程
- 包括技能学习、专题学习、假期作业等

### 教师模式 (teacher)
- 完成教师的授课任务
- 自动处理该教师班级的所有学生学习任务
- 自动重置学生密码（如果需要）

### 管理员模式 (admin)
- 批量处理所有教师账号
- 可选择是否只处理教师，或同时处理学生
- 自动重置教师和学生密码（如果需要）

## 🛠️ 开发工具

### PDM脚本命令

```bash
# 启动Web界面
pdm run web

# 启动命令行版本
pdm run start

# 运行测试
pdm run test

# 代码格式化
pdm run format

# 代码检查
pdm run lint

# 构建项目
pdm run build
```

### 项目结构

```
安全教育平台/
├── safety_education/          # 主要代码包
│   ├── __init__.py           # 包初始化
│   ├── config.py             # 配置管理
│   ├── core.py               # 核心类定义
│   └── automation.py         # 自动化逻辑
├── templates/                # Web界面模板
│   └── index.html           # 主页面
├── app.py                   # Web应用入口
├── index.py                 # 命令行入口
├── start_web.py             # Web启动脚本
├── pyproject.toml           # PDM项目配置
├── requirements.txt         # pip依赖文件
├── config.ini.example       # 配置文件模板
└── README.md               # 项目说明
```

## 🎨 Web界面特性

- **现代化设计**: 使用Bootstrap 5和渐变色设计
- **实时监控**: WebSocket实时更新任务状态和日志
- **进度显示**: 可视化进度条和任务计数
- **响应式布局**: 支持桌面和移动设备
- **交互友好**: 直观的操作界面和状态提示

## 🔧 主要改进

### 包管理优化
- 使用PDM进行现代化包管理
- 支持开发依赖和可选依赖
- 提供便捷的脚本命令

### 代码结构优化
- 模块化包结构
- 类型注解完善
- 配置管理独立
- 错误处理增强

### 用户体验改善
- Web可视化界面
- 实时进度监控
- 详细的日志输出
- 多种启动方式

## ⚠️ 注意事项

1. **保持原有URL和参数**: 所有网络请求的URL和参数都保持不变，确保功能正常
2. **配置文件安全**: 请妥善保管配置文件，避免密码泄露
3. **网络环境**: 确保网络连接稳定，脚本会自动处理临时网络问题
4. **端口占用**: Web界面默认使用5000端口，确保端口未被占用
5. **Python版本**: 要求Python 3.8或更高版本

## 🐛 故障排除

### 常见问题

1. **依赖安装失败**
   ```bash
   # 升级PDM
   pip install --upgrade pdm
   
   # 清除缓存重新安装
   pdm cache clear
   pdm install
   ```

2. **端口被占用**
   ```bash
   # 查看端口占用
   netstat -ano | findstr :5000
   
   # 或修改app.py中的端口号
   ```

3. **配置文件错误**
   ```bash
   # 重新创建配置文件
   cp config.ini.example config.ini
   ```

### 日志分析

脚本会输出详细的日志信息，包括：
- 登录状态和错误信息
- 课程完成情况
- 网络请求重试情况
- 任务执行进度

根据日志信息可以快速定位和解决问题。

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**享受自动化学习的便利！** 🎓✨
