# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['start_web.py'],
    pathex=[],
    binaries=[],
    datas=[('templates', 'templates'), ('config.ini.example', '.')],
    hiddenimports=['flask', 'flask_socketio', 'loguru', 'requests', 'beautifulsoup4'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='安全教育平台_Web版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
