@echo off
chcp 65001 >nul
title 安全教育平台自动化工具

echo.
echo ========================================
echo   安全教育平台自动化工具
echo ========================================
echo.

:menu
echo 请选择操作：
echo.
echo [1] 启动Web界面（推荐）
echo [2] 启动命令行版本
echo [3] 安装/更新依赖
echo [4] 创建配置文件
echo [5] 查看帮助
echo [0] 退出
echo.
set /p choice=请输入选项 (0-5): 

if "%choice%"=="1" goto web
if "%choice%"=="2" goto cli
if "%choice%"=="3" goto install
if "%choice%"=="4" goto config
if "%choice%"=="5" goto help
if "%choice%"=="0" goto exit
echo 无效选项，请重新选择
goto menu

:web
echo.
echo 🌐 启动Web界面...
echo 📱 浏览器将自动打开 http://localhost:5000
echo ⚡ 按 Ctrl+C 停止服务
echo.
python start_web.py
pause
goto menu

:cli
echo.
echo ⚡ 启动命令行版本...
echo.
if not exist config.ini (
    echo ❌ 配置文件不存在，请先创建配置文件
    echo.
    goto menu
)
python index.py
pause
goto menu

:install
echo.
echo 📦 安装/更新依赖...
echo.
where pdm >nul 2>nul
if %errorlevel%==0 (
    echo 使用PDM安装依赖...
    pdm install
) else (
    echo PDM未安装，使用pip安装依赖...
    pip install -r requirements.txt
)
echo.
echo ✅ 依赖安装完成
pause
goto menu

:config
echo.
echo 📄 创建配置文件...
echo.
if exist config.ini (
    echo ⚠️  配置文件已存在
    set /p overwrite=是否覆盖现有配置？(y/N): 
    if /i not "%overwrite%"=="y" goto menu
)
copy config.ini.example config.ini >nul
echo ✅ 配置文件已创建：config.ini
echo 📝 请编辑此文件填写您的账号信息
echo.
notepad config.ini
goto menu

:help
echo.
echo 📖 使用帮助
echo ========================================
echo.
echo 🌟 功能特点：
echo   • Web可视化界面，实时显示进度
echo   • 支持学生、教师、管理员三种角色
echo   • 自动完成所有学习任务
echo   • 完善的错误处理和重试机制
echo.
echo 📋 使用步骤：
echo   1. 选择选项4创建配置文件
echo   2. 编辑config.ini填写账号信息
echo   3. 选择选项1启动Web界面
echo   4. 在浏览器中操作
echo.
echo 🔧 故障排除：
echo   • 如果依赖安装失败，请检查网络连接
echo   • 如果端口被占用，请关闭其他程序
echo   • 如果登录失败，请检查账号密码
echo.
echo 📞 技术支持：
echo   • 查看README.md获取详细说明
echo   • 检查日志输出中的错误信息
echo.
pause
goto menu

:exit
echo.
echo 👋 感谢使用安全教育平台自动化工具！
echo.
exit /b 0
