<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>调试页面</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>快速选择用户测试</h3>
                <select class="form-select" id="savedUserSelect">
                    <option value="">手动输入或选择已保存的用户</option>
                </select>
                <div class="mt-3">
                    <label>用户名:</label>
                    <input type="text" class="form-control" id="username">
                </div>
                <div class="mt-3">
                    <label>密码:</label>
                    <input type="text" class="form-control" id="password">
                </div>
                <div class="mt-3">
                    <label>角色:</label>
                    <select class="form-select" id="role">
                        <option value="student">学生</option>
                        <option value="teacher">教师</option>
                        <option value="admin">管理员</option>
                    </select>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>管理员选择测试</h3>
                <select class="form-select" id="adminSelect">
                    <option value="">手动输入管理员信息</option>
                </select>
                <div class="mt-3">
                    <label>管理员账号:</label>
                    <input type="text" class="form-control" id="adminUsername">
                </div>
                <div class="mt-3">
                    <label>管理员密码:</label>
                    <input type="text" class="form-control" id="adminPassword">
                </div>
                <div class="mt-3">
                    <button class="btn btn-primary" id="fetchTeachersBtn">获取教师列表</button>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>调试信息</h3>
            <div id="debugInfo" class="alert alert-info"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const elements = {
            savedUserSelect: document.getElementById('savedUserSelect'),
            username: document.getElementById('username'),
            password: document.getElementById('password'),
            role: document.getElementById('role'),
            adminSelect: document.getElementById('adminSelect'),
            adminUsername: document.getElementById('adminUsername'),
            adminPassword: document.getElementById('adminPassword'),
            fetchTeachersBtn: document.getElementById('fetchTeachersBtn'),
            debugInfo: document.getElementById('debugInfo')
        };
        
        function log(message) {
            console.log(message);
            elements.debugInfo.innerHTML += message + '<br>';
        }
        
        // 快速选择用户事件
        elements.savedUserSelect.addEventListener('change', function() {
            const userId = this.value;
            log(`选择用户ID: ${userId}`);
            if (userId) {
                loadUserToForm(parseInt(userId));
            }
        });
        
        // 管理员选择事件
        elements.adminSelect.addEventListener('change', function() {
            const selectedValue = this.value;
            log(`选择管理员: ${selectedValue}`);
            if (selectedValue) {
                const [username, password] = selectedValue.split('|');
                elements.adminUsername.value = username;
                elements.adminPassword.value = password;
                log(`填充管理员信息: ${username} / ${password}`);
            } else {
                elements.adminUsername.value = '';
                elements.adminPassword.value = '';
            }
        });
        
        // 获取教师列表按钮
        elements.fetchTeachersBtn.addEventListener('click', function() {
            log('点击获取教师列表按钮');
            const adminUsername = elements.adminUsername.value.trim();
            const adminPassword = elements.adminPassword.value.trim();
            
            if (!adminUsername || !adminPassword) {
                log('管理员账号或密码为空');
                return;
            }
            
            log(`发送请求: ${adminUsername} / ${adminPassword}`);
            
            fetch('/api/teachers/fetch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: adminUsername,
                    password: adminPassword
                })
            })
            .then(response => {
                log(`响应状态: ${response.status}`);
                return response.json();
            })
            .then(data => {
                log(`响应数据: ${JSON.stringify(data)}`);
            })
            .catch(error => {
                log(`错误: ${error}`);
            });
        });
        
        // 加载用户到表单
        function loadUserToForm(userId) {
            log(`加载用户ID: ${userId}`);
            fetch('/api/users')
                .then(response => response.json())
                .then(data => {
                    log(`用户数据: ${JSON.stringify(data)}`);
                    if (data.success) {
                        const user = data.users.find(u => u.id === userId);
                        if (user) {
                            elements.username.value = user.username;
                            elements.password.value = user.password;
                            elements.role.value = user.role;
                            log(`填充用户信息: ${user.username} / ${user.password} / ${user.role}`);
                        } else {
                            log(`未找到用户ID: ${userId}`);
                        }
                    }
                })
                .catch(error => {
                    log(`加载用户信息失败: ${error}`);
                });
        }
        
        // 更新快速选择用户下拉框
        function updateSavedUserSelect(users) {
            const select = elements.savedUserSelect;
            select.innerHTML = '<option value="">手动输入或选择已保存的用户</option>';

            users.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.name} (${user.username}) - ${user.role}`;
                select.appendChild(option);
            });
            log(`更新用户下拉框: ${users.length} 个用户`);
        }
        
        // 更新管理员选择下拉框
        function updateAdminSelect() {
            fetch('/api/users')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const adminUsers = data.users.filter(user => user.role === 'admin');
                        const select = elements.adminSelect;
                        
                        select.innerHTML = '<option value="">手动输入管理员信息</option>';
                        
                        adminUsers.forEach(admin => {
                            const option = document.createElement('option');
                            option.value = `${admin.username}|${admin.password}`;
                            option.textContent = `${admin.name} (${admin.username})`;
                            select.appendChild(option);
                        });
                        log(`更新管理员下拉框: ${adminUsers.length} 个管理员`);
                    }
                })
                .catch(error => {
                    log(`加载管理员列表失败: ${error}`);
                });
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始初始化');
            
            // 加载用户列表
            fetch('/api/users')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateSavedUserSelect(data.users);
                    }
                })
                .catch(error => {
                    log(`加载用户列表失败: ${error}`);
                });
            
            // 更新管理员下拉框
            updateAdminSelect();
        });
    </script>
</body>
</html>
