#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import threading
import time
from datetime import datetime
from flask import Flask, render_template, request, jsonify, session
from flask_socketio import Socket<PERSON>, emit
from loguru import logger
import sys

# 导入原有的模块
from index import (
    Config, Student, Teacher, Admin,
    admin_learning, teacher_learning,
    _process_student_account, _complete_teacher_courses
)

# 导入数据库
from database import db

app = Flask(__name__)
app.config['SECRET_KEY'] = 'safety_education_platform_2024'
socketio = SocketIO(app, cors_allowed_origins="*")

# 全局变量存储任务状态
task_status = {
    'running': False,
    'progress': 0,
    'total': 0,
    'current_task': '',
    'logs': [],
    'start_time': None,
    'end_time': None
}

# 日志存储
log_messages = []

# 用户管理存储
saved_users = []

# 教师列表存储
teachers_cache = []

class WebLogger:
    """Web日志处理器，将日志发送到前端"""
    
    def __init__(self):
        self.logs = []
    
    def write(self, message):
        if message.strip():
            timestamp = datetime.now().strftime('%H:%M:%S')
            log_entry = f"[{timestamp}] {message.strip()}"

            # 存储到全局日志列表
            log_messages.append(log_entry)
            self.logs.append(log_entry)
            task_status['logs'].append(log_entry)

            # 限制日志数量，避免内存溢出
            if len(log_messages) > 1000:
                log_messages.pop(0)
            if len(self.logs) > 1000:
                self.logs.pop(0)
            if len(task_status['logs']) > 1000:
                task_status['logs'].pop(0)

            # 发送日志到前端
            socketio.emit('log_update', {
                'message': log_entry,
                'timestamp': timestamp
            })
    
    def flush(self):
        pass

# 创建Web日志处理器
web_logger = WebLogger()

@app.route('/')
def index():
    """主页"""
    return render_template('index_new.html')

@app.route('/old')
def old_index():
    """旧版主页"""
    return render_template('index.html')

@app.route('/test')
def test_page():
    """测试页面"""
    return render_template('test.html')

@app.route('/api/config', methods=['GET', 'POST'])
def handle_config():
    """处理配置信息"""
    if request.method == 'GET':
        # 读取配置文件
        try:
            config = Config.from_file('config.ini')
            return jsonify({
                'success': True,
                'data': {
                    'username': config.username,
                    'password': config.password,
                    'role': config.role,
                    'special_id': config.special_id,
                    'start_from_teacher': config.start_from_teacher,
                    'resume_from_breakpoint': config.resume_from_breakpoint
                }
            })
        except FileNotFoundError:
            return jsonify({
                'success': False,
                'message': '配置文件不存在'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'读取配置失败: {str(e)}'
            })
    
    elif request.method == 'POST':
        # 保存配置文件
        try:
            data = request.json
            config_content = f"""[Credentials]
username = {data['username']}
password = {data['password']}
role = {data['role']}
special_id = {data.get('special_id', '1178')}
start_from_teacher = {data.get('start_from_teacher', '')}
resume_from_breakpoint = {data.get('resume_from_breakpoint', False)}
"""
            with open('config.ini', 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            return jsonify({
                'success': True,
                'message': '配置保存成功'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'保存配置失败: {str(e)}'
            })

@app.route('/api/start', methods=['POST'])
def start_task():
    """启动学习任务"""
    if task_status['running']:
        return jsonify({
            'success': False,
            'message': '任务正在运行中，请等待完成'
        })
    
    try:
        data = request.json
        username = data.get('username')
        password = data.get('password')
        role = data.get('role')
        special_id = data.get('special_id', '1178')
        only_teacher = data.get('only_teacher', False)
        start_from_teacher = data.get('start_from_teacher', '')
        resume_from_breakpoint = data.get('resume_from_breakpoint', False)
        
        if not all([username, password, role]):
            return jsonify({
                'success': False,
                'message': '请填写完整的配置信息'
            })
        
        # 重置任务状态
        task_status.update({
            'running': True,
            'progress': 0,
            'total': 0,
            'current_task': '正在初始化...',
            'logs': [],
            'start_time': datetime.now(),
            'end_time': None
        })
        
        # 在新线程中执行任务
        thread = threading.Thread(
            target=run_learning_task,
            args=(username, password, role, special_id, only_teacher, start_from_teacher, resume_from_breakpoint)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'message': '任务已启动'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'启动任务失败: {str(e)}'
        })

@app.route('/api/status')
def get_status():
    """获取任务状态"""
    return jsonify(serialize_task_status(task_status))

@app.route('/api/logs')
def get_logs():
    """获取历史日志"""
    return jsonify({
        'success': True,
        'logs': log_messages
    })

@app.route('/api/users', methods=['GET', 'POST'])
def manage_users():
    """管理用户"""
    if request.method == 'GET':
        # 获取所有保存的用户
        try:
            users = db.get_users()
            return jsonify({
                'success': True,
                'users': users
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取用户失败: {str(e)}',
                'users': []
            })

    elif request.method == 'POST':
        # 添加用户
        try:
            data = request.json
            name = data.get('name', '').strip()
            username = data.get('username', '').strip()
            password = data.get('password', '').strip()
            role = data.get('role', 'student')

            if not all([name, username, password]):
                return jsonify({
                    'success': False,
                    'message': '请填写完整的用户信息'
                })

            user_id = db.add_user(name, username, password, role)
            if user_id:
                return jsonify({
                    'success': True,
                    'message': '用户添加成功',
                    'user_id': user_id
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '用户名已存在'
                })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'添加用户失败: {str(e)}'
            })

@app.route('/api/users/<int:user_id>', methods=['PUT', 'DELETE'])
def manage_user(user_id):
    """管理单个用户"""
    if request.method == 'PUT':
        # 更新用户
        try:
            data = request.json
            name = data.get('name', '').strip()
            username = data.get('username', '').strip()
            password = data.get('password', '').strip()
            role = data.get('role', 'student')

            if not all([name, username, password]):
                return jsonify({
                    'success': False,
                    'message': '请填写完整的用户信息'
                })

            success = db.update_user(user_id, name, username, password, role)
            if success:
                return jsonify({
                    'success': True,
                    'message': '用户更新成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '用户不存在或用户名已被使用'
                })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'更新用户失败: {str(e)}'
            })

    elif request.method == 'DELETE':
        # 删除用户
        try:
            success = db.delete_user(user_id)
            if success:
                return jsonify({
                    'success': True,
                    'message': '用户删除成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '用户不存在'
                })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'删除用户失败: {str(e)}'
            })

@app.route('/api/teachers')
def get_teachers():
    """获取缓存的教师列表"""
    try:
        admin_username = request.args.get('admin_username')
        teachers = db.get_teachers(admin_username)
        return jsonify({
            'success': True,
            'teachers': teachers
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取教师列表失败: {str(e)}',
            'teachers': []
        })

@app.route('/api/teachers/fetch', methods=['POST'])
def fetch_teachers():
    """通过管理员账号获取教师列表"""
    try:
        data = request.json
        admin_username = data.get('username')
        admin_password = data.get('password')

        if not admin_username or not admin_password:
            return jsonify({
                'success': False,
                'message': '请提供管理员账号和密码'
            })

        # 使用管理员账号登录并获取教师列表
        admin = Admin(admin_username, admin_password)
        admin.login()

        teacher_list = admin.get_teacher_list()

        # 转换教师数据格式
        formatted_teachers = []
        for teacher_info in teacher_list:
            formatted_teacher = {
                'username': teacher_info.get('username', ''),
                'nickname': teacher_info.get('nickname', ''),
                'school': teacher_info.get('school', ''),
                'class_count': teacher_info.get('class_count', 0),
                'student_count': teacher_info.get('student_count', 0)
            }
            formatted_teachers.append(formatted_teacher)

        # 保存教师列表到数据库
        db.save_teachers(formatted_teachers, admin_username)

        return jsonify({
            'success': True,
            'teachers': formatted_teachers,
            'message': f'成功获取 {len(formatted_teachers)} 个教师信息'
        })

    except Exception as e:
        logger.error(f'获取教师列表失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取教师列表失败: {str(e)}',
            'teachers': []
        })

@app.route('/api/teachers/clear', methods=['POST'])
def clear_teachers():
    """清空教师列表"""
    try:
        data = request.json
        admin_username = data.get('admin_username')

        count = db.clear_teachers(admin_username)
        return jsonify({
            'success': True,
            'message': f'已清空 {count} 个教师记录'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'清空教师列表失败: {str(e)}'
        })

@app.route('/api/projects')
def get_projects():
    """获取缓存的项目列表"""
    try:
        # 获取所有项目，不区分管理员
        projects = db.get_projects()
        return jsonify({
            'success': True,
            'projects': projects
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取项目列表失败: {str(e)}',
            'projects': []
        })

@app.route('/api/projects/fetch', methods=['POST'])
def fetch_projects():
    """通过管理员账号获取项目列表"""
    try:
        data = request.json
        admin_username = data.get('username')
        admin_password = data.get('password')

        if not admin_username or not admin_password:
            return jsonify({
                'success': False,
                'message': '请提供管理员账号和密码'
            })

        # 使用管理员账号登录并获取项目列表
        admin = Admin(admin_username, admin_password)
        admin.login()

        project_list = admin.get_projects_list()

        # 保存项目列表到数据库
        db.save_projects(project_list, admin_username)

        # 保存完成情况数据
        for project in project_list:
            if project.get('special_id') and project.get('status') == 'active':
                completion_data = {
                    'teacher_total': project.get('teacher_total', 0),
                    'teacher_completed': project.get('teacher_completed', 0),
                    'teacher_completion_rate': project.get('teacher_completion_rate', 0),
                    'student_total': project.get('student_total', 0),
                    'student_completed': project.get('student_completed', 0),
                    'student_completion_rate': project.get('student_completion_rate', 0),
                    'teacher_details': project.get('teacher_details', [])
                }
                db.update_project_completion(project['special_id'], admin_username, completion_data)

        return jsonify({
            'success': True,
            'projects': project_list,
            'message': f'成功获取 {len(project_list)} 个项目信息'
        })

    except Exception as e:
        logger.error(f'获取项目列表失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取项目列表失败: {str(e)}',
            'projects': []
        })

@app.route('/api/projects/clear', methods=['POST'])
def clear_projects():
    """清空项目列表"""
    try:
        data = request.json
        admin_username = data.get('admin_username')

        count = db.clear_projects(admin_username)
        return jsonify({
            'success': True,
            'message': f'已清空 {count} 个项目记录'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'清空项目列表失败: {str(e)}'
        })

@app.route('/api/projects/<special_id>/completion', methods=['POST'])
def get_project_completion(special_id):
    """获取项目完成情况"""
    try:
        data = request.json
        admin_username = data.get('username')

        if not admin_username:
            return jsonify({
                'success': False,
                'message': '请提供管理员账号'
            })

        # 从数据库获取完成情况
        completion_details = db.get_project_completion_details(special_id, admin_username)

        if not completion_details:
            return jsonify({
                'success': False,
                'message': '未找到项目完成情况数据，请先获取项目列表'
            })

        # 转换为前端需要的格式
        incomplete_teachers = []
        completed_teachers = []

        for teacher in completion_details['teacher_details']:
            teacher_info = {
                'username': teacher['username'] or teacher['name'],  # 如果没有用户名，使用姓名
                'nickname': teacher['name'],
                'school': teacher['school'],
                'teacher_completed': teacher['teacher_completed'] == teacher['teacher_total'],
                'student_count': teacher['student_total'],
                'completed_students': teacher['student_completed'],
                'completion_rate': teacher['completion_rate'],
                'all_completed': teacher['status'] == 'completed'
            }

            if teacher['status'] == 'completed':
                completed_teachers.append(teacher_info)
            else:
                incomplete_teachers.append(teacher_info)

        completion_status = {
            'special_id': special_id,
            'total_teachers': completion_details['teacher_total'],
            'completed_teachers_count': len(completed_teachers),
            'incomplete_teachers_count': len(incomplete_teachers),
            'teacher_completion_rate': completion_details['teacher_completion_rate'],
            'total_students': completion_details['student_total'],
            'completed_students_count': completion_details['student_completed'],
            'student_completion_rate': completion_details['student_completion_rate'],
            'completed_teachers': completed_teachers,
            'incomplete_teachers': incomplete_teachers
        }

        return jsonify({
            'success': True,
            'completion_status': completion_status
        })

    except Exception as e:
        logger.error(f'获取项目完成情况失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'获取项目完成情况失败: {str(e)}'
        })

@app.route('/api/execute-teachers', methods=['POST'])
def execute_teachers():
    """批量执行教师学习任务"""
    if task_status['running']:
        return jsonify({
            'success': False,
            'message': '任务正在运行中，请等待完成'
        })

    try:
        data = request.json
        teachers = data.get('teachers', [])
        admin_username = data.get('admin_username')
        admin_password = data.get('admin_password')
        special_id = data.get('special_id', '1178')

        if not teachers:
            return jsonify({
                'success': False,
                'message': '请选择要执行的教师'
            })

        if not admin_username or not admin_password:
            return jsonify({
                'success': False,
                'message': '请提供管理员账号和密码'
            })

        # 创建任务记录
        task_id = db.create_task_execution('batch_teachers', teachers, len(teachers))

        # 重置任务状态
        task_status.update({
            'running': True,
            'progress': 0,
            'total': len(teachers),
            'current_task': '正在初始化批量任务...',
            'logs': [],
            'start_time': datetime.now(),
            'end_time': None
        })

        # 在新线程中执行批量任务
        thread = threading.Thread(
            target=run_batch_teachers_task,
            args=(task_id, teachers, admin_username, admin_password, special_id)
        )
        thread.daemon = True
        thread.start()

        return jsonify({
            'success': True,
            'message': f'批量任务已启动，共 {len(teachers)} 个教师',
            'task_id': task_id
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'启动批量任务失败: {str(e)}'
        })

@app.route('/api/stop', methods=['POST'])
def stop_task():
    """停止任务"""
    task_status['running'] = False
    task_status['current_task'] = '任务已停止'
    task_status['end_time'] = datetime.now()

    return jsonify({
        'success': True,
        'message': '任务已停止'
    })

def serialize_task_status(status):
    """序列化任务状态，处理datetime对象"""
    status_copy = status.copy()

    # 转换datetime对象为字符串
    if status_copy.get('start_time'):
        status_copy['start_time'] = status_copy['start_time'].strftime('%H:%M:%S')
    if status_copy.get('end_time'):
        status_copy['end_time'] = status_copy['end_time'].strftime('%H:%M:%S')

    return status_copy

def run_learning_task(username, password, role, special_id='1178', only_teacher=False, start_from_teacher='', resume_from_breakpoint=False):
    """运行学习任务"""
    try:
        # 重定向日志输出
        original_stdout = sys.stdout
        sys.stdout = web_logger
        
        # 配置loguru输出到自定义处理器
        logger.remove()
        logger.add(web_logger.write, format="{message}", level="INFO")
        
        task_status['current_task'] = f'正在以{role}身份登录...'
        socketio.emit('status_update', serialize_task_status(task_status))
        
        if role == 'student':
            run_student_mode(username, password, special_id, resume_from_breakpoint)
        elif role == 'teacher':
            run_teacher_mode(username, password, special_id, only_teacher, resume_from_breakpoint)
        elif role == 'admin':
            run_admin_mode(username, password, special_id, only_teacher, start_from_teacher, resume_from_breakpoint)
        else:
            raise ValueError(f'不支持的角色类型: {role}')
        
        task_status['current_task'] = '所有任务已完成'
        task_status['progress'] = task_status['total']
        
    except Exception as e:
        logger.error(f'任务执行失败: {str(e)}')
        task_status['current_task'] = f'任务失败: {str(e)}'
    
    finally:
        task_status['running'] = False
        task_status['end_time'] = datetime.now()
        sys.stdout = original_stdout
        socketio.emit('status_update', serialize_task_status(task_status))

def run_student_mode(username, password, special_id='1178', resume_from_breakpoint=False):
    """学生模式"""
    task_status['total'] = 1
    task_status['current_task'] = '学生模式：正在完成课程...'

    student = Student(username, password)
    student.login()
    student.complete_all_courses()

    task_status['progress'] = 1

def run_teacher_mode(username, password, special_id='1178', only_teacher=False, resume_from_breakpoint=False):
    """教师模式"""
    teacher = Teacher(username, password)
    teacher.login()
    
    # 获取学生列表来计算总任务数
    student_list = [] if only_teacher else teacher.get_student_list()
    task_status['total'] = 1 + len(student_list)  # 1个教师 + N个学生
    
    # 完成教师课程
    task_status['current_task'] = f'正在完成教师 {teacher.nickname} 的课程...'
    _complete_teacher_courses(teacher, special_id)
    task_status['progress'] = 1
    socketio.emit('status_update', serialize_task_status(task_status))
    
    if not only_teacher:
        # 完成学生课程
        teacher_info = teacher.get_user_info()
        for i, student_info in enumerate(student_list):
            task_status['current_task'] = f'正在处理学生 {student_info.get("nickname", "未知")} ({i+1}/{len(student_list)})'
            socketio.emit('status_update', serialize_task_status(task_status))
            
            try:
                _process_student_account(teacher, teacher_info, student_info)
                task_status['progress'] = 1 + i + 1
            except Exception as e:
                logger.error(f'处理学生失败: {str(e)}')
                continue

def run_admin_mode(username, password, special_id='1178', only_teacher=False, start_from_teacher='', resume_from_breakpoint=False):
    """管理员模式"""
    admin = Admin(username, password)
    admin.login()

    teacher_list = admin.get_teacher_list()

    # 如果指定了开始教师，找到起始位置
    start_index = 0
    if start_from_teacher:
        for i, teacher_info in enumerate(teacher_list):
            if teacher_info.get('username') == start_from_teacher:
                start_index = i
                logger.info(f'从指定教师开始：{start_from_teacher} (第{i+1}个)')
                break
        else:
            logger.warning(f'未找到指定的教师：{start_from_teacher}，将从头开始')
            start_index = 0

    # 更新任务总数和当前状态
    remaining_teachers = teacher_list[start_index:]
    task_status['total'] = len(remaining_teachers)
    if start_from_teacher and start_index > 0:
        task_status['current_task'] = f'管理员模式：从教师 {start_from_teacher} 开始，剩余 {len(remaining_teachers)} 个教师账号'
    else:
        task_status['current_task'] = f'管理员模式：找到 {len(teacher_list)} 个教师账号'

    for i, teacher_info in enumerate(remaining_teachers):
        if not task_status['running']:  # 检查是否被停止
            break

        actual_index = start_index + i + 1
        task_status['current_task'] = f'正在处理教师 {teacher_info.get("username", "未知")} ({actual_index}/{len(teacher_list)})'
        task_status['progress'] = i
        socketio.emit('status_update', serialize_task_status(task_status))

        try:
            teacher_learning(teacher_info['username'], special_id=special_id, only_teacher=only_teacher)
        except Exception as e:
            logger.error(f'处理教师失败: {str(e)}')
            continue

    task_status['progress'] = len(remaining_teachers)

def run_batch_teachers_task(task_id, teachers, admin_username, admin_password, special_id='1178'):
    """运行批量教师任务"""
    original_stdout = sys.stdout
    try:
        logger.info(f'开始批量执行任务，任务ID: {task_id}')
        logger.info(f'教师列表: {teachers}')
        logger.info(f'管理员: {admin_username}')

        # 重定向日志输出
        sys.stdout = web_logger

        # 配置loguru输出到自定义处理器
        logger.remove()
        logger.add(web_logger.write, format="{message}", level="INFO")

        # 更新任务状态
        db.update_task_execution(task_id, status='running')

        task_status['current_task'] = f'批量执行模式：共 {len(teachers)} 个教师'
        socketio.emit('status_update', serialize_task_status(task_status))

        # 首先使用管理员账号登录
        logger.info(f'使用管理员账号登录: {admin_username}')
        admin = Admin(admin_username, admin_password)
        admin.login()

        # 获取完整的教师列表信息
        teacher_list = admin.get_teacher_list()
        teacher_info_map = {t['username']: t for t in teacher_list}

        completed = 0
        failed = 0

        for i, teacher_username in enumerate(teachers):
            if not task_status['running']:  # 检查是否被停止
                logger.info('任务被用户停止')
                break

            task_status['current_task'] = f'正在处理教师 {teacher_username} ({i+1}/{len(teachers)})'
            task_status['progress'] = i
            socketio.emit('status_update', serialize_task_status(task_status))

            try:
                logger.info(f'开始处理教师: {teacher_username}')

                # 检查教师是否在列表中
                if teacher_username not in teacher_info_map:
                    logger.error(f'教师 {teacher_username} 不在管理员的教师列表中')
                    failed += 1
                    continue

                teacher_info = teacher_info_map[teacher_username]
                logger.info(f'处理教师 {teacher_username} ({teacher_info.get("nickname", "未知")})')

                # 使用管理员模式处理教师账号，类似于_process_teacher_account函数
                teacher = Teacher(teacher_username, 'WCLhqxx2022')  # 使用默认密码

                try:
                    # 尝试登录
                    teacher.login()
                    logger.info(f'教师 {teacher_username} 登录成功')
                except Exception as login_error:
                    logger.warning(f'教师 {teacher_username} 登录失败，尝试重置密码: {login_error}')
                    try:
                        # 登录失败，尝试重置密码
                        admin_info = admin.get_user_info()
                        tmp_info = admin_info.copy()
                        resp = admin.get_teacher_info(teacher_info)
                        tmp_info.update(resp)

                        # 重置密码
                        reset_result = teacher.change_password(tmp_info)
                        logger.info(f'教师 {teacher_username} 密码重置结果: {reset_result}')

                        # 重新登录
                        teacher.login()
                        logger.info(f'教师 {teacher_username} 重置密码后登录成功')
                    except Exception as reset_error:
                        logger.error(f'教师 {teacher_username} 密码重置失败: {reset_error}')
                        failed += 1
                        continue

                # 完成教师课程
                logger.info(f'开始完成教师 {teacher_username} 的课程')
                _complete_teacher_courses(teacher, special_id)

                # 完成学生课程
                logger.info(f'开始处理教师 {teacher_username} 的学生')
                student_list = teacher.get_student_list()
                teacher_user_info = teacher.get_user_info()

                student_success = 0
                student_failed = 0
                for student_info in student_list:
                    try:
                        _process_student_account(teacher, teacher_user_info, student_info)
                        student_success += 1
                    except Exception as e:
                        student_failed += 1
                        logger.error(f'处理学生 {student_info.get("nickname", "未知")} 失败: {str(e)}')
                        continue

                completed += 1
                logger.info(f'教师 {teacher_username} 处理完成，学生成功: {student_success}, 失败: {student_failed}')

            except Exception as e:
                failed += 1
                logger.error(f'处理教师 {teacher_username} 失败: {str(e)}')
                # 继续处理下一个教师，不中断整个批量任务
                continue

            # 更新数据库中的任务进度
            db.update_task_execution(task_id, progress=completed)

        # 任务完成
        task_status['current_task'] = f'批量任务完成！成功: {completed}, 失败: {failed}, 总计: {len(teachers)}'
        task_status['progress'] = len(teachers)

        # 更新任务状态为完成
        final_status = 'completed' if failed == 0 else 'completed_with_errors'
        db.update_task_execution(task_id, status=final_status, progress=completed)

        logger.info(f'批量任务执行完成，成功: {completed}, 失败: {failed}')

    except Exception as e:
        error_msg = f'批量任务执行失败: {str(e)}'
        logger.error(error_msg)
        task_status['current_task'] = error_msg
        db.update_task_execution(task_id, status='failed', error_message=str(e))

    finally:
        task_status['running'] = False
        task_status['end_time'] = datetime.now()
        sys.stdout = original_stdout
        socketio.emit('status_update', serialize_task_status(task_status))
        logger.info('批量任务线程结束')

@socketio.on('connect')
def handle_connect():
    """客户端连接"""
    emit('status_update', serialize_task_status(task_status))
    # 发送历史日志
    for log_message in log_messages:
        emit('log_update', {'message': log_message})

if __name__ == '__main__':
    print("🚀 安全教育平台Web界面启动中...")
    print("📱 请在浏览器中访问: http://localhost:5000")
    print("⚡ 按 Ctrl+C 停止服务")
    socketio.run(app, host='0.0.0.0', port=5000, debug=False)
