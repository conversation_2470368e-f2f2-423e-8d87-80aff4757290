@echo off
chcp 65001 >nul
title ��ȫ����ƽ̨ - �޸���

echo ��ȫ����ƽ̨�Զ������� (�޸���)
echo ================================
echo.
echo [1] ����Web����
echo [2] ���������а汾
echo [3] ���������ļ�
echo [0] �˳�
echo.
set /p choice=��ѡ��: 

if "%choice%"=="1" (
    echo ����Web����...
    cd Web��
    "��ȫ����ƽ̨_Web��.exe"
    cd ..
) else if "%choice%"=="2" (
    echo ���������а汾...
    if not exist config.ini (
        echo ���ȴ��������ļ���
        pause
        goto :eof
    )
    cd �����а�
    "��ȫ����ƽ̨_�����а�.exe"
    cd ..
) else if "%choice%"=="3" (
    echo ���������ļ�...
    if exist config.ini (
        echo �����ļ��Ѵ��ڣ��Ƿ񸲸ǣ�(y/N)
        set /p overwrite=
        if /i not "%overwrite%"=="y" goto :eof
    )
    copy config.ini.example config.ini
    echo �����ļ��Ѵ�������༭��д�˺���Ϣ
    notepad config.ini
) else if "%choice%"=="0" (
    exit
) else (
    echo ��Чѡ��
)

pause
