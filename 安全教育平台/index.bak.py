import configparser
import time
import requests
import json
import re
import random
from functools import reduce
from datetime import datetime
from loguru import logger
from bs4 import BeautifulSoup
import urllib3

urllib3.disable_warnings()

__all__ = [
    'main'
]


class Info:
    def __init__(self):
        self.session = requests.Session()
        self.session.verify = False
        # self.session.proxies = {
        #     'http': 'http://127.0.0.1:7898',
        #     'https': 'https://127.0.0.1:7898',
        # }
        self.session.headers[
            'User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36'
        self.land_marks = ''

    def get_prv_list(self) -> list:
        """
        获取省份信息列表
        :return:{
                "ID": 14,
                "PrvName": "安徽省",
                "AddTime": "2010-07-08 10:03:45",
                "Status": 1
                }
        """
        url = "https://chongqing.xueanquan.com/WebApi/api/safearea/prv-list"
        response = self.session.get(url).json()
        self.land_marks = response['Landmarks']
        return response['Data']

    def get_city_list(self, prv_id) -> list:
        """
        获取城市信息列表
        :return: {
                "ID": 460001,
                "CityName": "重庆市",
                "PrvID": 46,
                "AreaCode": "",
                "Status": 1,
                "AddTime": "2011-01-05 15:42:49"
            }
        """
        url = "https://chongqing.xueanquan.com/WebApi/api/area/city-list"
        querystring = {"jsoncallback": "", "prvId": prv_id}
        response = self.session.get(url, params=querystring)
        return response.json()

    def get_country_list(self, city_id) -> list:
        """
        获取地区信息列表
        :return:{
            "ID": 305042705,
            "CountryName": "万州区",
            "CityID": 460001,
            "Status": 1,
            "AddTime": "2011-01-05 15:43:10",
            "Display": 1
            }
        """
        url = "https://chongqing.xueanquan.com/WebApi/api/area/county-list"
        querystring = {"jsoncallback": "", "cityId": city_id}
        response = self.session.get(url, params=querystring)
        return response.json()

    def get_school_list(self, country_id) -> list:
        """
        获取学校信息列表
        :return:{
            "ID": 332310710,
            "SchoolName": "白云小学",
            "CountryCode": 305042722,
            "TownsID": 8431,
            "Status": 1,
            "AddTime": "2017-09-14 10:01:07",
            "StartGrade": 1,
            "EndGrade": 6,
            "SchoolType": 2,
            "GradeType": 21,
            "SchoolProperty": 1,
            "SchoolAddress": "白云小学",
            "LengthOfSchooling": 1
        }
        """
        url = "https://chongqing.xueanquan.com/WebApi/api/area/school-list"
        querystring = {"jsoncallback": "", "countryId": country_id}
        response = self.session.get(url, params=querystring)
        return response.json()

    def get_grade_list(self, school_id) -> list:
        """
        获取年级信息列表
        :return:{
            "Grade": 1,
            "GradeName": "一年级"
            }
        """
        url = "https://chongqing.xueanquan.com/WebApi/api/area/grade-list"
        querystring = {"jsoncallback": "", "schoolId": school_id}
        response = self.session.get(url, params=querystring)
        return response.json()

    def get_class_list(self, school_id, grade_id) -> list:
        """
        获取班级信息列表
        :return:{
            "ID": 233641,
            "ClassName": "3班",
            "SchoolID": 60102,
            "Status": 1,
            "AddTime": "2019-09-05 09:01:14",
            "Grade": 4
        }
        """
        url = "https://chongqing.xueanquan.com/WebApi/api/area/classroom-list"
        querystring = {"jsoncallback": "", "schoolId": school_id, "grade": grade_id}
        response = self.session.get(url, params=querystring)
        return response.json()


class User(Info):
    def __init__(self, username='', password=''):
        # self.session.verify = False
        super().__init__()
        self.username = username.strip()
        self.password = password.strip()
        self.nickname = '未知'
        self.is_success = False
        self.token = ''

    def get_user_id(self, teacher_info: dict):
        """
        获取用户ID 如：7B94E3F4A0985AE82BC111270569F10421AED32C031B955A
        :return:{
            "Status": true,
            "UserName": "ligongxun2883",
            "DesUserId": "7B94E3F4A0985AE82BC111270569F10421AED32C031B955A"
        }
        """

        self.get_prv_list()
        time.sleep(0.2)
        url = "https://chongqing.xueanquan.com/WebApi/api/safearea/select-userinfo"
        querystring = {
            "TrueName": teacher_info['trueName'],
            "UserType": teacher_info.get('UserType', 0),
            "Prv": teacher_info['prvCode'],
            "Account": teacher_info['userName'],
            "City": teacher_info['cityCode'],
            "Country": teacher_info['countyId'],
            "School": teacher_info['schoolId'],
            "Grade": teacher_info['grade'],
            "ClassRoom": teacher_info['classRoom']
        }
        response = self.session.get(url, params=querystring, headers={
            'Landmarks': self.land_marks
        })
        data = response.json()
        self.land_marks = data['Landmarks']
        return data['Data']['DesUserId']

    def get_user_success_code(self, user_name, user_id):
        """
        获取用户成功码
        :param user_name:ligongxun2883
        :param user_id: 7B94E3F4A0985AE82BC111270569F10421AED32C031B955A
        :return:{
            "Result": true,
            "Message": "DFA897B0172D13244F49C5B41018CD54B9E9B637CF97E519"
        }
        """
        url = "https://chongqing.xueanquan.com/WebApi/api/safearea/find-usercard"
        querystring = {
            "UseName": user_name,
            "DescUserId": user_id
        }
        response = self.session.post(url, data=querystring, headers={
            "Landmarks": self.land_marks
        })
        data = response.json()
        self.land_marks = data['Landmarks']
        return data['Message']

    def get_user_info(self) -> dict:
        url = "https://huodongapi.xueanquan.com/p/chongqing/Topic/topic/platformapi/api/v1/users/user-info"
        resp = self.session.get(url)
        userinfo = resp.json()
        return userinfo

    def login(self, username='', password='', is_logger=True) -> requests.Session:
        """
        登陆
        :param is_logger: 是否打印日志
        :param username: 用户名
        :param password: 密码
        :return: Session
        """
        url = "https://appapi.xueanquan.com/usercenter/api/v3/wx/login"
        querystring = {"checkShowQrCode": "true", "tmp": "false"}
        payload = {
            "username": username or self.username,
            "password": password or self.password,
            "loginOrigin": 1
        }
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36",
            "Accept-Encoding": "deflate, gzip",
            "content-type": "application/json"
        }
        resp = self.session.post(url=url, json=payload, headers=headers, params=querystring)
        data = resp.json()
        if data['err_code'] == 8:
            logger.error(f"{self.username}\t{data['err_desc']}")
            if data['err_desc'].find('登录失败') != -1:
                logger.warning('哦豁，登录太频繁了，休息15分钟后继续')
                time.sleep(15 * 60)
                return self.login(username, password, is_logger)
            else:
                raise Exception(data['err_desc'])
        elif data['err_code'] != 0:
            logger.error(f"{self.username}\t{data['err_desc']}")

            raise Exception(data['err_desc'])
        # 登录成功
        self.session.headers['Authorization'] = data['data']['token']
        # 配置个人信息
        self.token = data['data']['token']
        self.nickname = data['data']['trueName']
        self.is_success = True
        if is_logger:
            logger.success(f'{self.nickname}\t登陆成功')
        return self.session

    def _get_password_params(self, teacher_info: dict):
        user_id = self.get_user_id(teacher_info)
        time.sleep(0.5)
        success_code = self.get_user_success_code(user_name=teacher_info['userName'], user_id=user_id)
        time.sleep(0.5)
        return {
            "user_id": user_id,
            "success_code": success_code
        }

    def change_password(self, teacher_info: dict, password='WCLhqxx2022'):
        """
        修改密码
        :param teacher_info:{
         'province_id',city_id country_id school_id grade_id class_id
        }
        :param password:
        :return: ({Status:'False',Msg:'修改成功',code:'1'})
        """
        user_info = self._get_password_params(teacher_info)
        # url = "https://chongqing.xueanquan.com/WebApi/Holiday/PwdChangeByUserSecurity"
        url = "https://chongqing.xueanquan.com/WebApi/api/safearea/PwdChangeByUserSecurity"
        querystring = {
            "newpwd": password,
            "DescUserId": user_info['user_id'],
            "DesSuccess": user_info['success_code']
        }
        # response = self.session.get(url, params=querystring)
        response = self.session.post(url, data=querystring, headers={
            "Landmarks": self.land_marks
        }).json()
        if response.get('ErrorMsg', '').find('10分钟') != -1:
            logger.error(f"{self.username}\t{response.get('ErrorMsg')}")
            logger.info('你也看到了，我现在要去休息10分钟了')
            time.sleep(60 * 10)
            return self.change_password(teacher_info, password)
        # logger.info(response.text)
        return response

    def _get_special_id(self, course_url: str):
        """
        获取专题学习id
        :return:
        """
        # 通过js文件查找
        course_id = course_url.split('/')[-2]
        url = f'https://huodong.xueanquan.com/{course_id}/js/common.js'
        resp = self.session.get(url)
        specialid = re.findall(r'specialId: (\d+),', resp.text)
        if len(specialid) > 0:
            return specialid[-1]
        # 找不到就去页面body中查找
        resp = self.session.get(course_url.replace('index.html', 'xuexiao.html'))
        soup = BeautifulSoup(resp.content, 'lxml')
        specialid = soup.body.get('data-specialid')
        if not specialid:
            raise Exception('获取specialid失败')
        return specialid
        # return '1152'


class Student(User):
    def complete(self):
        course_list = self.get_courses()
        for course in course_list:
            if course['sort'] == 'Skill':
                self.complete_skill_course(course)
            # 暂时跳过专题学习
            elif course['sort'] == 'Special':
                self.complete_special_course(course)
            elif course['sort'] == 'SummerWinterHoliday':
                self.complete_holiday_course(course)
                pass
            time.sleep(0.5)

    def _complete_video(self, course_id, grade_id):
        url = "https://yyapi.xueanquan.com/chongqing/api/v1/StudentHomeWork/VideoPlayRecordSave"
        querystring = {"courseId": course_id, "gradeId": grade_id}
        self.session.post(url, params=querystring)
        pass

    def _get_video_info(self, course_id):
        url = "https://yyapi.xueanquan.com/chongqing/api/v1/StudentHomeWork/VideoInfoGet"
        querystring = {"courseId": course_id}
        data = self.session.get(url, params=querystring).json()['result']
        return {
            "fid": data['fid'],
            'work_id': data['workId']
        }

    def complete_holiday_course(self, course):
        now = datetime.now()
        url = "https://huodongapi.xueanquan.com/p/chongqing/Topic/topic/platformapi/api/v1/holiday/sign"
        special_id = self._get_special_id(course['url'])
        for step in range(1, 3):
            payload = {
                "schoolYear": now.year,
                "semester": 2 if now.month > 5 else 1,
                "step": step,
                'specialId': special_id
            }
            resp = self.session.post(url, json=payload, headers={
                'Referer': 'https://huodong.xueanquan.com/'
            }).json()
        if (resp.get('result')):
            logger.success(f'{self.nickname}\t{course["title"]}\t{resp.get("msg", "学习完毕")}')
        else:
            logger.error(f'{self.nickname}\t{course["title"]}{resp.get("msg", "学习失败")}')

    def complete_skill_course(self, course):
        """
        完成安全学习
        :return:
        """
        course_id = re.findall(r'li=(\d+)', course['url'])[0]
        grade_id = re.findall(r'gid=(\d+)', course['url'])[0]
        video_info = self._get_video_info(course_id)
        self._complete_video(course_id, grade_id)
        url = "https://yyapi.xueanquan.com/chongqing/api/v1/StudentHomeWork/HomeWorkSign"
        payload = {
            "workId": video_info['work_id'],
            "fid": video_info['fid'],
            "testanswer": "0|0|0",
            "testinfo": "已掌握技能",
            "testMark": 100,
            "testResult": 1,
            "courseId": course_id,
        }
        resp = self.session.post(url, json=payload).json()
        if resp['success']:
            logger.success(f'{self.nickname}\t{course["title"]}学习完毕')
        else:
            logger.error(f'{self.nickname}\t{course["title"]}学习失败')
        pass

    def complete_special_course(self, course):
        """
        完成专题学习
        :return:
        """
        headers = {
            'Referer': 'https://huodong.xueanquan.com/'
        }
        url = 'https://huodongapi.xueanquan.com/p/chongqing/Topic/topic/platformapi/api/v1/records/sign'
        # self.session.get(course['url'])
        special_id = self._get_special_id(course['url'])
        temp1 = self.session.post(url, data={
            "specialId": special_id,
            "step": 1
        }, headers=headers)
        temp2 = self.session.post(url, data={
            "specialId": special_id,
            "step": 2
        }, headers=headers)
        try:
            self.submit_special_test(special_id)
        except IndexError as e:
            logger.info('跳过专题测试问卷')
        # self.session.get(course['url'])
        if self.check_finish(special_id):
            logger.success(f'{self.nickname} \t {course["title"]}\t完成签到')
        else:
            logger.error(f'{self.nickname} \t {course["title"]}\t签到失败')
        pass

    def submit_special_test(self, special_id):
        """
        提交专题测试问卷
        :return:
        """
        question = self._get_special_question(special_id)
        user = self.get_user_info()
        post_data = {
            'user': user,
            'UserAnswers': question,
            'specialId': special_id,
            'step': 2,
            "countyId": user['countyId']
        }
        url = 'https://huodongapi.xueanquan.com/Topic/topic/main/api/v1/records/v3/survey'
        self.session.post(url, json=post_data)

    @staticmethod
    def _choice_answer(item):
        """
        从选项中随机选择专题测试答案
        :param item:
        :return:
        """
        if item['radioType'] == 'Radio':
            return random.choice(item['option'])['item']
        return ','.join(list(map(lambda x: x['item'], random.choices(item['option']))))

    def _get_special_question(self, special_id):
        """
        获取专题测试问题
        :return:
        """
        url = "https://huodongapi.xueanquan.com/Topic/topic/main/api/v1/records/questions"
        querystring = {"specialId": special_id, "grade": "2", "region": "0"}
        resp = self.session.get(url, params=querystring)
        data = resp.json()['result'][0]
        question_list = list(map(lambda item: {
            'module': data['module'],
            'id': item['id'],
            'type': item['radioType'],
            'answer': self._choice_answer(item),
            'tagId': item['tagId'],
        }, data['question']))
        pass
        return question_list

    def get_courses(self) -> list:
        """
        获取未完成课程
        :return: List
        """
        url = 'https://yyapi.xueanquan.com/chongqing/safeapph5/api/v1/homework/homeworklist'
        resp = self.session.get(url)
        # 过滤出未完成课程
        return list(filter(lambda item: item.get('workStatus', '') == 'UnFinish', resp.json()))
        pass

    def check_finish(self, special_id):
        """
        检测学生是否完成
        :param special_id:
        :return:
        """
        url = 'https://huodongapi.xueanquan.com/p/chongqing/Topic/topic/platformapi/api/v1/records/finish-status'
        resp = self.session.get(url, params={
            "specialId": special_id
        }).json()
        if resp:
            return resp['finishStatus']
        return False


class Teacher(User):
    def get_student_list(self, page=1):
        """
        获取学生账号等信息
        :return:
        """
        data_list = []
        url = "https://chongqing.xueanquan.com/eduadmin/ClassManagement/ClassManagement"
        resp = self.session.post(url, data={"pageNum": page, 'numPerPage': 100})
        soup = BeautifulSoup(resp.text, 'lxml')
        for item in soup.select('table> tbody >tr'):
            data_list.append({
                'nickname': item.select('td')[1].text.strip(),
                'student_id': item.get('rel').split('/')[0],
                'username': item.get('rel').split('/')[1],
                'status': item.select('td')[3].text.strip(),
                'landmarks': item.select_one('a').get('href').split('=')[-1]
            })
        return data_list

    def get_teach_course_ids(self, user_info: dict) -> list:
        """
        获取已学习课程ID
        :param user_info:
        :return:
        """
        url = "https://chongqing.xueanquan.com/ajax/EPlatform.SafeSchool.TeacherCourse,EPlatform.ashx"
        querystring = {"_method": "TeaCourse", "_session": "rw"}
        payload = f"userid={user_info['userID']}\r\nclassroom={user_info['classRoom']}\r\ngg={user_info['grade']}"
        resp = self.session.post(url, data=payload, params=querystring)
        data = json.loads(resp.text.replace('\'', '\"'))
        return list(map(lambda item: item['courseid'], data['Rows']))
        pass

    def get_courses(self) -> list:
        """
        获取未完成课程
        :return:
        """
        grade_id = self._get_grade_id()
        user_info = self.get_user_info()
        teach_course_ids = self.get_teach_course_ids(user_info)
        url = "https://chongqing.xueanquan.com/ajax/EPlatform.SafeSchool.TeacherCourse,EPlatform.ashx"
        resp = self.session.post(url, params={"_method": "I_CoursesGet"},
                                 data=f"gradeid={grade_id}\r\ngrade={user_info['grade']}")
        data = json.loads(resp.text.replace('\'', '\"').replace('new Date(1,0,1,0,0,0)', '""'))
        # data_list = reduce(lambda x, y: x['Courses'] + y['Courses'], data)
        all_course_list = reduce(lambda x, y: x + y['Courses'], data, [])
        return list(filter(lambda item: item['CourseID'] not in teach_course_ids, all_course_list))

    def _get_grade_id(self):
        url = 'https://chongqing.xueanquan.com/SafeSchool/TeacherCourse.aspx'
        resp = self.session.get(url)
        return re.findall(r'grade\s*=\s*"(\d+)"', resp.text)[0]

    def _get_fid(self, course_id: str):
        url = "https://yyapi.xueanquan.com/chongqing/ajax/EPlatform.SafeSchool.TeacherCourseText,EPlatform.aspx"
        querystring = {"_method": "TempGetByCourseID"}
        resp = self.session.post(url, json=course_id, params=querystring).json()
        return resp['fid']

    def complete_skill_course(self, course_info: dict):
        """
        完成安全授课
        :return:
        """
        url = "https://yyapi.xueanquan.com/chongqing/ajax/EPlatform.SafeSchool.TeacherCourseText,EPlatform.aspx"
        querystring = {"_method": "InsertCourseStatus_New"}
        payload = {
            "couserId": course_info['CourseID'],
            "gradeId": course_info['GradeID'],
            "fid": self._get_fid(course_info['CourseID']),
            "title": course_info['CourseName']
        }
        response = self.session.post(url, json=payload, params=querystring)
        logger.info(response.text)

    def _get_special_url(self) -> str:
        # resp = self.session.get('https://chongqing.xueanquan.com/MainPage.html')
        # soup = BeautifulSoup(resp.text, 'lxml')
        resp = self.session.get(
            f'https://put.xueanquan.com/angel/push/imageslide.html?location=1&siteid=0&r={int(time.time() / 3600)}&host=chongqing.xueanquan.com')
        soup = BeautifulSoup(resp.text, 'lxml')
        return soup.select('a')[0].get('href', '')

    def complete_special_course(self, special_id=None):
        """
        完成专题授课
        :return:
        """
        try:

            headers = {
                'Referer': 'https://huodong.xueanquan.com/'
            }
            url = 'https://huodongapi.xueanquan.com/p/chongqing/Topic/topic/platformapi/api/v1/records/sign'
            # self.session.get(course['url'])
            if not special_id:
                special_url = self._get_special_url()
                special_id = self._get_special_id(special_url)
            temp1 = self.session.post(url, data={
                "specialId": special_id,
                "step": 1
            }, headers=headers)
            temp2 = self.session.post(url, data={
                "specialId": special_id,
                "step": 2
            }, headers=headers)
            logger.success(f'教师{self.nickname} \t 完成签到')
        except IndexError as e:
            logger.error(e)

    def check_finish(self) -> bool:
        """
        检测教师是否完成授课
        :return:
        """
        url = 'https://chongqing.xueanquan.com/ajax/EPlatform.SafeSchool.TeacherCourseText,EPlatform.ashx'
        resp = self.session.post(url, params={"_method": "TeachStudySituationProgress", "_session": "rw"},
                                 data=f"liID=593\r\ngid=486")
        data = json.loads(resp.text.replace('\'', '\"'))
        return bool(data['Rows'][0]['skillStatus'])

    def change_teacher_password(self, password='Mxy012522', old_password=''):
        url = 'https://chongqing.xueanquan.com/eduadmin/UserInfo/UserPwdModifySave'
        resp = self.session.post(url, data={
            'OldPWD': old_password or self.password,
            'NewPWD1': password,
            'NewPWD2': password,
            'ajax': 1
        })
        if resp.ok:
            logger.success(resp.json()['message'])
            return resp.json()
        pass

    def reset_student_password(self, student_id, landmarks):
        """
        重置学生密码
        :param landmarks:
        :param student_id:
        :return:
        """
        url = "https://chongqing.xueanquan.com/eduadmin/ClassManagement/StudentPassWordReset"
        querystring = {"studentid": student_id}
        resp = self.session.post(url, params=querystring, headers={
            'landmarks': landmarks,
        }).json()
        pass


class Admin(Teacher):
    def reset_teacher_password(self, student_id, landmarks):
        pass

    def get_teacher_list(self):
        """
        获取教师账号等信息
        :return:
        """
        data_list = []
        url = "https://chongqing.xueanquan.com/eduadmin/TeacherManagement/TeacherManage"
        resp = self.session.post(url, data={"pageNum": 1, 'numPerPage': 100})
        soup = BeautifulSoup(resp.text, 'lxml')
        for item in soup.select('table> tbody >tr'):
            data_list.append({
                'nickname': item.select('td')[1].text.strip(),
                'student_id': item.get('rel').split('/')[0],
                'username': item.get('rel').split('/')[1],
                'class': item.select('td')[3].text.strip(),
                'landmarks': item.select_one('a').get('href').split('=')[-1]
            })
        return data_list

    def get_teacher_info(self, teacher_info):
        """
        获取教师年级，班级
        :return:
        """
        url = f'https://chongqing.xueanquan.com/eduadmin/TeacherManagement/TeacherEdit/{teacher_info["student_id"]}/{teacher_info["username"]}'
        resp = self.session.get(url)
        resp.encoding = 'utf-8'
        soup = BeautifulSoup(resp.text, 'lxml')
        return {
            'grade': soup.select_one('#TE_Grade1 > option[selected]').get('value'),
            'classRoom': soup.select_one('#combox_ManagementClassRoom1 > option[selected]').get('value'),
            'trueName': teacher_info['nickname'],
            'userName': teacher_info['username'],
            'UserType': 1,

        }
        pass

    def _get_student_count(self):
        """
        获取学生总数量
        :return:
        """
        url = "https://chongqing.xueanquan.com/eduadmin/ClassManagement/ClassManagement"
        resp = self.session.post(url, data={"pageNum": 1, 'numPerPage': 10})
        soup = BeautifulSoup(resp.text, 'lxml')
        return 1

    def get_student_by_class(self, class_id: str):
        """
        获取所有学生数据，根据第一页获取到总的数据条数，然后解析出所有数据，并yield
        :param class_id:
        :return:
        """
        url = "https://chongqing.xueanquan.com/eduadmin/ClassManagement/ClassManagement_School"
        # 将keywords参数进行url编码
        keywords = str(["", class_id, "", ""])
        data = f'keywords={keywords}&pageNum=1&numPerPage=100'
        response = self.session.post(url, data=data, headers={
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        })
        soup = BeautifulSoup(response.text, 'lxml')
        for item in soup.select('table> tbody >tr'):
            yield {
                'nickname': item.select('td')[1].text.strip(),
                'student_id': item.get('rel').split('/')[0],
                'username': item.get('rel').split('/')[1],
                'status': item.select('td')[3].text.strip(),
                'landmarks': item.select_one('a').get('href').split('=')[-1],
                'classRoom': class_id
            }

    def get_student_list(self, page=1):
        url = "https://chongqing.xueanquan.com/eduadmin/ClassManagement/ClassManagement"
        resp = self.session.post(url, data={"pageNum": page, 'numPerPage': 100})
        soup = BeautifulSoup(resp.text, 'lxml')
        for item in soup.select('table> tbody >tr'):
            yield {
                'nickname': item.select('td')[1].text.strip(),
                'student_id': item.get('rel').split('/')[0],
                'username': item.get('rel').split('/')[1],
                'classRoom': item.get('rel').split('/')[2],
                'status': item.select('td')[3].text.strip(),
                'landmarks': item.select_one('a').get('href').split('=')[-1],
            }


def admin_learning_student():
    admin = Admin('zhengxiangyong8', 'WCLhqxx1923')
    admin.login()
    admin_info = admin.get_user_info()
    admin.get_student_by_class('366935')
    # 1. 获取所有班级信息

    # 2. 遍历班级信息获取对于班级所有学生账号
    # 3. 遍历学生账号实现批量学习
    pass


def admin_learning(admin_name, admin_password, special_id=None, only_teacher=False):
    """
    通过管理员账号学习
    :return:
    """
    admin = Admin(admin_name, admin_password)
    admin.login()
    admin_info = admin.get_user_info()
    teacher_list = admin.get_teacher_list()
    for teacher_info in teacher_list[:]:
        teacher = Teacher(teacher_info['username'], 'WCLhqxx2022')
        try:
            teacher.login()
            # raise Exception()
        except Exception as e:
            tmp_info = admin_info.copy()
            resp = admin.get_teacher_info(teacher_info)
            tmp_info.update(resp)
            a = teacher.change_password(tmp_info)
            pass
        teacher_learning(teacher_info['username'], special_id=special_id, only_teacher=only_teacher)
        pass


def teacher_learning(teacher_name, teacher_password='WCLhqxx2022', special_id=None, only_teacher=False):
    """
    通过教师账号学习
    :return:
    """
    teacher = Teacher(teacher_name, teacher_password)
    # teacher = Teacher(' zhongqing5621', ' Z1234qwer')
    teacher.login()
    logger.success(f'教师{teacher.nickname}登陆成功')
    teacher_info = teacher.get_user_info()
    # 完成教师授课(安全平台）
    course_list = teacher.get_courses()
    if course_list:
        # 完成安全授课
        teacher.complete_skill_course(course_list[0])
    else:
        logger.info(f'{teacher.nickname}\t已完成所有安全课程')
    # 完成专题授课
    teacher.complete_special_course(special_id=special_id)
    if only_teacher:
        return
    # 完成学生安全学习
    for student_info in teacher.get_student_list():
        # 跳过未激活账号
        if student_info.get('status') == '未激活':
            logger.error(f"教师：{teacher_info['trueName']}\t学生：{student_info['username']}\t账号未激活，请注意提醒！！！")
            continue
        # 默认直接登陆，出错->尝试教师重置密码
        student = Student(student_info['username'], 'WCLhqxx2022')
        # student = Student('yangxinyi3582956', 'Mxy012522')

        try:
            student.login()
        except Exception as e:
            student.nickname = student_info['nickname']
            tmp_info = teacher_info.copy()
            tmp_info.update({
                'trueName': student_info['nickname'],
                'userName': student_info['username'],
            })
            # student.nickname = '杨欣怡'
            a = student.change_password(tmp_info, 'WCLhqxx2022')
            student.login(password='WCLhqxx2022')
        # 学生开始完成安全学习
        student.complete()


def read_config(file_path):
    config = configparser.ConfigParser()
    config.read(file_path, encoding='utf-8')
    return config['Credentials']['username'], config['Credentials']['password'], config['Credentials']['role']


def test():
    student = Student('liukefei2504', 'WCLhqxx2022')
    student.login()
    student.complete()


def main():
    username, password, role = read_config('config.ini')
    if role == 'admin':
         # only_teacher = input('是否只学习教师？(y/n)')
        admin_learning(username, password, '1178', only_teacher=False)
    elif role == 'teacher':
        teacher_learning(username, password)
    else:
        raise Exception('role参数错误，只可选择teacher|admin')
    # admin_learning('zhengxiangyong8', 'WCLhqxx1923')

    # test()


if __name__ == '__main__':
    main()
    # teacher_learning('zhongqing5621', 'Z1234qwer')
