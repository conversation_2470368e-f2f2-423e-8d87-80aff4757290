#!/bin/bash

# 安全教育平台自动化工具启动脚本
# 适用于 Linux/macOS 系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示标题
show_title() {
    echo -e "${BLUE}"
    echo "========================================"
    echo "   安全教育平台自动化工具"
    echo "========================================"
    echo -e "${NC}"
}

# 显示菜单
show_menu() {
    echo -e "${CYAN}请选择操作：${NC}"
    echo
    echo -e "${GREEN}[1]${NC} 启动Web界面（推荐）"
    echo -e "${GREEN}[2]${NC} 启动命令行版本"
    echo -e "${GREEN}[3]${NC} 安装/更新依赖"
    echo -e "${GREEN}[4]${NC} 创建配置文件"
    echo -e "${GREEN}[5]${NC} 查看帮助"
    echo -e "${GREEN}[0]${NC} 退出"
    echo
}

# 启动Web界面
start_web() {
    echo -e "${BLUE}🌐 启动Web界面...${NC}"
    echo -e "${YELLOW}📱 浏览器将自动打开 http://localhost:5000${NC}"
    echo -e "${YELLOW}⚡ 按 Ctrl+C 停止服务${NC}"
    echo
    python3 start_web.py
}

# 启动命令行版本
start_cli() {
    echo -e "${BLUE}⚡ 启动命令行版本...${NC}"
    echo
    if [ ! -f "config.ini" ]; then
        echo -e "${RED}❌ 配置文件不存在，请先创建配置文件${NC}"
        return 1
    fi
    python3 index.py
}

# 安装依赖
install_deps() {
    echo -e "${BLUE}📦 安装/更新依赖...${NC}"
    echo
    
    if command -v pdm &> /dev/null; then
        echo -e "${GREEN}使用PDM安装依赖...${NC}"
        pdm install
    else
        echo -e "${YELLOW}PDM未安装，使用pip安装依赖...${NC}"
        pip3 install -r requirements.txt
    fi
    
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
}

# 创建配置文件
create_config() {
    echo -e "${BLUE}📄 创建配置文件...${NC}"
    echo
    
    if [ -f "config.ini" ]; then
        echo -e "${YELLOW}⚠️  配置文件已存在${NC}"
        read -p "是否覆盖现有配置？(y/N): " overwrite
        if [[ ! "$overwrite" =~ ^[Yy]$ ]]; then
            return 0
        fi
    fi
    
    cp config.ini.example config.ini
    echo -e "${GREEN}✅ 配置文件已创建：config.ini${NC}"
    echo -e "${YELLOW}📝 请编辑此文件填写您的账号信息${NC}"
    echo
    
    # 尝试打开编辑器
    if command -v nano &> /dev/null; then
        nano config.ini
    elif command -v vim &> /dev/null; then
        vim config.ini
    elif command -v vi &> /dev/null; then
        vi config.ini
    else
        echo -e "${YELLOW}请手动编辑 config.ini 文件${NC}"
    fi
}

# 显示帮助
show_help() {
    echo -e "${BLUE}📖 使用帮助${NC}"
    echo "========================================"
    echo
    echo -e "${PURPLE}🌟 功能特点：${NC}"
    echo "  • Web可视化界面，实时显示进度"
    echo "  • 支持学生、教师、管理员三种角色"
    echo "  • 自动完成所有学习任务"
    echo "  • 完善的错误处理和重试机制"
    echo
    echo -e "${PURPLE}📋 使用步骤：${NC}"
    echo "  1. 选择选项4创建配置文件"
    echo "  2. 编辑config.ini填写账号信息"
    echo "  3. 选择选项1启动Web界面"
    echo "  4. 在浏览器中操作"
    echo
    echo -e "${PURPLE}🔧 故障排除：${NC}"
    echo "  • 如果依赖安装失败，请检查网络连接"
    echo "  • 如果端口被占用，请关闭其他程序"
    echo "  • 如果登录失败，请检查账号密码"
    echo
    echo -e "${PURPLE}📞 技术支持：${NC}"
    echo "  • 查看README.md获取详细说明"
    echo "  • 检查日志输出中的错误信息"
    echo
}

# 主循环
main() {
    show_title
    
    while true; do
        show_menu
        read -p "请输入选项 (0-5): " choice
        echo
        
        case $choice in
            1)
                start_web
                ;;
            2)
                start_cli
                ;;
            3)
                install_deps
                ;;
            4)
                create_config
                ;;
            5)
                show_help
                ;;
            0)
                echo -e "${GREEN}👋 感谢使用安全教育平台自动化工具！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}无效选项，请重新选择${NC}"
                ;;
        esac
        
        echo
        read -p "按回车键继续..."
        echo
    done
}

# 检查Python
check_python() {
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ 错误：未找到Python3${NC}"
        echo -e "${YELLOW}请安装Python 3.8或更高版本${NC}"
        exit 1
    fi
    
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    echo -e "${GREEN}✅ Python版本：$python_version${NC}"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    check_python
    main
fi
