@echo off
chcp 65001 >nul
title 安全教育平台 - 一键打包工具

echo.
echo ========================================
echo   安全教育平台 - 一键打包工具
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 📦 开始打包流程...
echo.

python build_package.py

if %errorlevel% equ 0 (
    echo.
    echo 🎉 打包完成！
    echo.
    echo 📁 打包文件位置：dist_package 目录
    echo 📦 可以将ZIP文件分享给其他用户
    echo.
    echo 💡 用户使用方法：
    echo    1. 解压ZIP文件
    echo    2. 双击"启动程序.bat"
    echo    3. 按提示创建配置文件
    echo    4. 启动Web界面使用
    echo.
) else (
    echo.
    echo ❌ 打包失败！
    echo 请检查上方的错误信息
    echo.
)

pause
