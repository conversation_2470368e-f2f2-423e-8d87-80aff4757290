#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复版打包脚本 - 解决依赖问题
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def main():
    """修复版打包流程"""
    print("🔧 安全教育平台 - 修复版打包工具")
    print("=" * 50)
    
    project_root = Path(__file__).parent
    
    # 检查必要文件
    required_files = ['start_web.py', 'index.py', 'app.py', 'database.py']
    missing_files = [f for f in required_files if not (project_root / f).exists()]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    # 安装必要依赖
    print("📦 安装必要依赖...")
    dependencies = [
        'pyinstaller>=5.0',
        'loguru>=0.5.0',
        'beautifulsoup4>=4.13.4',
        'lxml>=4.6.0',
        'requests>=2.25.0',
        'flask>=2.0.0',
        'flask-socketio>=5.0.0',
    ]
    
    for dep in dependencies:
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                          check=True, capture_output=True)
            print(f"   ✅ {dep}")
        except subprocess.CalledProcessError:
            print(f"   ⚠️ {dep} 安装失败")
    
    # 清理旧文件
    print("🧹 清理旧文件...")
    for dir_name in ['build', 'dist', '__pycache__', '修复版打包结果']:
        dir_path = project_root / dir_name
        if dir_path.exists():
            shutil.rmtree(dir_path)
    
    # 删除旧的spec文件
    for spec_file in project_root.glob("*.spec"):
        spec_file.unlink()
    
    # 构建Web版本 - 使用目录模式而不是单文件模式
    print("🌐 构建Web版本...")
    web_cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onedir',  # 使用目录模式
        '--console',
        '--name=安全教育平台_Web版',
        '--add-data=templates;templates',
        '--add-data=config.ini.example;.',
        '--hidden-import=flask',
        '--hidden-import=flask_socketio',
        '--hidden-import=socketio',
        '--hidden-import=engineio',
        '--hidden-import=loguru',
        '--hidden-import=requests',
        '--hidden-import=bs4',
        '--hidden-import=beautifulsoup4',
        '--hidden-import=lxml',
        '--hidden-import=sqlite3',
        '--hidden-import=configparser',
        '--hidden-import=threading',
        '--hidden-import=json',
        '--hidden-import=datetime',
        '--hidden-import=database',
        '--hidden-import=app',
        '--hidden-import=index',
        '--collect-all=loguru',
        '--collect-all=bs4',
        '--collect-all=lxml',
        'start_web.py'
    ]
    
    try:
        result = subprocess.run(web_cmd, check=True, cwd=project_root, 
                               capture_output=True, text=True)
        print("✅ Web版本构建完成")
    except subprocess.CalledProcessError as e:
        print("❌ Web版本构建失败")
        print("错误输出:", e.stderr)
        return False
    
    # 构建命令行版本
    print("⚡ 构建命令行版本...")
    cli_cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onedir',  # 使用目录模式
        '--console',
        '--name=安全教育平台_命令行版',
        '--add-data=config.ini.example;.',
        '--hidden-import=loguru',
        '--hidden-import=requests',
        '--hidden-import=bs4',
        '--hidden-import=beautifulsoup4',
        '--hidden-import=lxml',
        '--hidden-import=sqlite3',
        '--hidden-import=configparser',
        '--hidden-import=database',
        '--collect-all=loguru',
        '--collect-all=bs4',
        '--collect-all=lxml',
        'index.py'
    ]
    
    try:
        result = subprocess.run(cli_cmd, check=True, cwd=project_root,
                               capture_output=True, text=True)
        print("✅ 命令行版本构建完成")
    except subprocess.CalledProcessError as e:
        print("❌ 命令行版本构建失败")
        print("错误输出:", e.stderr)
        return False
    
    # 创建发布目录
    print("📁 创建发布包...")
    dist_dir = project_root / "修复版打包结果"
    dist_dir.mkdir(exist_ok=True)
    
    # 复制可执行文件目录
    web_dist = project_root / "dist" / "安全教育平台_Web版"
    cli_dist = project_root / "dist" / "安全教育平台_命令行版"
    
    if web_dist.exists():
        shutil.copytree(web_dist, dist_dir / "Web版", dirs_exist_ok=True)
    if cli_dist.exists():
        shutil.copytree(cli_dist, dist_dir / "命令行版", dirs_exist_ok=True)
    
    # 复制配置文件
    config_file = project_root / "config.ini.example"
    if config_file.exists():
        shutil.copy2(config_file, dist_dir)
    
    # 创建启动脚本
    startup_script = '''@echo off
chcp 65001 >nul
title 安全教育平台 - 修复版

echo 安全教育平台自动化工具 (修复版)
echo ================================
echo.
echo [1] 启动Web界面
echo [2] 启动命令行版本
echo [3] 创建配置文件
echo [0] 退出
echo.
set /p choice=请选择: 

if "%choice%"=="1" (
    echo 启动Web界面...
    cd Web版
    "安全教育平台_Web版.exe"
    cd ..
) else if "%choice%"=="2" (
    echo 启动命令行版本...
    if not exist config.ini (
        echo 请先创建配置文件！
        pause
        goto :eof
    )
    cd 命令行版
    "安全教育平台_命令行版.exe"
    cd ..
) else if "%choice%"=="3" (
    echo 创建配置文件...
    if exist config.ini (
        echo 配置文件已存在，是否覆盖？(y/N)
        set /p overwrite=
        if /i not "%overwrite%"=="y" goto :eof
    )
    copy config.ini.example config.ini
    echo 配置文件已创建，请编辑填写账号信息
    notepad config.ini
) else if "%choice%"=="0" (
    exit
) else (
    echo 无效选择
)

pause
'''
    
    with open(dist_dir / "启动.bat", 'w', encoding='gbk') as f:
        f.write(startup_script)
    
    # 创建说明文件
    readme = '''# 安全教育平台 - 修复版

## 使用方法

1. 双击 `启动.bat`
2. 选择启动方式
3. 首次使用请先创建配置文件

## 文件说明

- `Web版/` - Web界面版本目录
- `命令行版/` - 命令行版本目录
- `config.ini.example` - 配置文件模板
- `启动.bat` - 启动脚本

## 配置说明

选择选项3创建配置文件，然后编辑填写：

```
[Credentials]
username = 你的用户名
password = 你的密码
role = admin
```

## 注意事项

- 此版本使用目录模式打包，包含所有依赖
- 如果遇到问题，请查看控制台错误信息
- Web版本启动后会显示访问地址
'''
    
    with open(dist_dir / "README.txt", 'w', encoding='utf-8') as f:
        f.write(readme)
    
    print("=" * 50)
    print("🎉 修复版打包完成！")
    print(f"📁 文件位置: {dist_dir}")
    print("📋 包含文件:")
    for item in dist_dir.iterdir():
        print(f"   - {item.name}")
    print("=" * 50)
    print("💡 使用说明:")
    print("1. 进入修复版打包结果目录")
    print("2. 双击启动.bat")
    print("3. 选择启动方式")
    print("4. 首次使用需要创建配置文件")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        input("按回车键退出...")
    else:
        input("打包失败，按回车键退出...")
