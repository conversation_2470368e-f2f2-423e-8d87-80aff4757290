#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
安全教育平台打包脚本
用于创建可分发的Windows可执行文件包
"""

import os
import sys
import shutil
import subprocess
import zipfile
from pathlib import Path
from datetime import datetime

class PackageBuilder:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.build_dir = self.project_root / "build_package"
        self.dist_dir = self.project_root / "dist_package"
        self.package_name = "安全教育平台_v2.0"
        
    def clean_build(self):
        """清理构建目录"""
        print("🧹 清理构建目录...")
        
        # 清理PyInstaller生成的目录
        for dir_name in ['build', 'dist', '__pycache__']:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                shutil.rmtree(dir_path)
                print(f"   删除: {dir_path}")
        
        # 清理自定义构建目录
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
            
        print("✅ 构建目录清理完成")
    
    def install_dependencies(self):
        """安装打包依赖"""
        print("📦 安装打包依赖...")
        
        dependencies = [
            'pyinstaller>=5.0',
            'auto-py-to-exe>=2.0',
        ]
        
        for dep in dependencies:
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                             check=True, capture_output=True)
                print(f"   ✅ {dep}")
            except subprocess.CalledProcessError as e:
                print(f"   ❌ {dep} 安装失败: {e}")
                return False
        
        print("✅ 打包依赖安装完成")
        return True
    
    def create_spec_file(self):
        """创建PyInstaller spec文件"""
        print("📝 创建PyInstaller配置文件...")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# Web应用打包配置
web_app = Analysis(
    ['start_web.py'],
    pathex=['{self.project_root.as_posix()}'],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('config.ini.example', '.'),
        ('README.md', '.'),
    ],
    hiddenimports=[
        'flask',
        'flask_socketio',
        'socketio',
        'engineio',
        'loguru',
        'requests',
        'beautifulsoup4',
        'lxml',
        'sqlite3',
        'configparser',
        'threading',
        'json',
        'datetime',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 命令行应用打包配置
cli_app = Analysis(
    ['index.py'],
    pathex=['{self.project_root.as_posix()}'],
    binaries=[],
    datas=[
        ('config.ini.example', '.'),
    ],
    hiddenimports=[
        'loguru',
        'requests',
        'beautifulsoup4',
        'lxml',
        'sqlite3',
        'configparser',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 合并分析结果
MERGE((web_app, 'start_web', 'start_web'), (cli_app, 'index', 'index'))

# Web应用可执行文件
web_pyz = PYZ(web_app.pure, web_app.zipped_data, cipher=block_cipher)
web_exe = EXE(
    web_pyz,
    web_app.scripts,
    [],
    exclude_binaries=True,
    name='安全教育平台_Web界面',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

# 命令行应用可执行文件
cli_pyz = PYZ(cli_app.pure, cli_app.zipped_data, cipher=block_cipher)
cli_exe = EXE(
    cli_pyz,
    cli_app.scripts,
    [],
    exclude_binaries=True,
    name='安全教育平台_命令行',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

# 收集所有文件
coll = COLLECT(
    web_exe,
    web_app.binaries,
    web_app.zipfiles,
    web_app.datas,
    cli_exe,
    cli_app.binaries,
    cli_app.zipfiles,
    cli_app.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='{self.package_name}',
)
'''
        
        spec_file = self.project_root / "package.spec"
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print(f"✅ 配置文件已创建: {spec_file}")
        return spec_file
    
    def build_executables(self, spec_file):
        """构建可执行文件"""
        print("🔨 构建可执行文件...")
        
        try:
            # 运行PyInstaller
            cmd = [sys.executable, '-m', 'PyInstaller', '--clean', str(spec_file)]
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"❌ PyInstaller构建失败:")
                print(result.stderr)
                return False
            
            print("✅ 可执行文件构建完成")
            return True
            
        except Exception as e:
            print(f"❌ 构建过程出错: {e}")
            return False
    
    def create_package_structure(self):
        """创建最终的包结构"""
        print("📁 创建包结构...")
        
        # 创建目标目录
        self.dist_dir.mkdir(exist_ok=True)
        package_dir = self.dist_dir / self.package_name
        
        if package_dir.exists():
            shutil.rmtree(package_dir)
        package_dir.mkdir()
        
        # 复制PyInstaller生成的文件
        pyinstaller_dist = self.project_root / "dist" / self.package_name
        if pyinstaller_dist.exists():
            shutil.copytree(pyinstaller_dist, package_dir / "程序文件", dirs_exist_ok=True)
        
        # 复制必要的配置文件
        files_to_copy = [
            'config.ini.example',
            'README.md',
        ]
        
        for file_name in files_to_copy:
            src_file = self.project_root / file_name
            if src_file.exists():
                shutil.copy2(src_file, package_dir)
        
        # 创建数据目录
        (package_dir / "数据").mkdir(exist_ok=True)
        
        print(f"✅ 包结构创建完成: {package_dir}")
        return package_dir
