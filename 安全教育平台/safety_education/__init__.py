#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
安全教育平台自动化工具

一个功能强大的安全教育平台自动化工具，提供命令行和Web界面两种使用方式，
支持学生、教师和管理员三种模式的自动学习。

Features:
- Web可视化界面
- 命令行支持
- 多角色支持（学生/教师/管理员）
- 实时进度监控
- 自动错误处理和重试
- 配置文件管理
"""

__version__ = "2.0.0"
__author__ = "Safety Education Platform Team"
__email__ = "<EMAIL>"
__license__ = "MIT"

from .config import Config
from .core import BaseAPI, User, Student, Teacher, Admin
from .automation import (
    SafetyEducationPlatform,
    admin_learning,
    teacher_learning,
)

__all__ = [
    # Version info
    "__version__",
    "__author__",
    "__email__",
    "__license__",
    
    # Core classes
    "Config",
    "BaseAPI", 
    "User",
    "Student",
    "Teacher", 
    "Admin",
    
    # Automation functions
    "SafetyEducationPlatform",
    "admin_learning",
    "teacher_learning",
]
