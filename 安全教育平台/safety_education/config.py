#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置管理模块

提供配置文件的读取、写入和验证功能。
"""

import configparser
from dataclasses import dataclass
from pathlib import Path
from typing import Optional


@dataclass
class Config:
    """配置类，用于存储用户配置信息"""
    username: str
    password: str
    role: str
    special_id: str = '1178'
    start_from_teacher: str = ''
    resume_from_breakpoint: bool = False
    default_password: str = 'WCLhqxx2022'
    
    def __post_init__(self):
        """验证配置参数"""
        if not self.username.strip():
            raise ValueError("用户名不能为空")
        if not self.password.strip():
            raise ValueError("密码不能为空")
        if self.role not in ['student', 'teacher', 'admin']:
            raise ValueError(f"不支持的角色类型: {self.role}")
    
    @classmethod
    def from_file(cls, file_path: str = 'config.ini') -> 'Config':
        """从配置文件读取配置"""
        config_file = Path(file_path)
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {file_path}")
        
        config = configparser.ConfigParser()
        config.read(file_path, encoding='utf-8')
        
        if 'Credentials' not in config:
            raise ValueError("配置文件格式错误：缺少 [Credentials] 节")
        
        credentials = config['Credentials']
        required_keys = ['username', 'password', 'role']
        
        for key in required_keys:
            if key not in credentials:
                raise ValueError(f"配置文件缺少必需的配置项: {key}")
        
        return cls(
            username=credentials['username'].strip(),
            password=credentials['password'].strip(),
            role=credentials['role'].strip(),
            special_id=credentials.get('special_id', '1178').strip(),
            start_from_teacher=credentials.get('start_from_teacher', '').strip(),
            resume_from_breakpoint=credentials.getboolean('resume_from_breakpoint', fallback=False)
        )
    
    def to_file(self, file_path: str = 'config.ini') -> None:
        """将配置保存到文件"""
        config = configparser.ConfigParser()
        config['Credentials'] = {
            'username': self.username,
            'password': self.password,
            'role': self.role,
            'special_id': self.special_id,
            'start_from_teacher': self.start_from_teacher,
            'resume_from_breakpoint': str(self.resume_from_breakpoint)
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            config.write(f)
    
    @classmethod
    def create_example(cls, file_path: str = 'config.ini.example') -> None:
        """创建示例配置文件"""
        example_content = """[Credentials]
# 用户名
username = your_username_here
# 密码
password = your_password_here
# 角色类型: admin(管理员) / teacher(教师) / student(学生)
role = teacher

# 配置说明:
# 1. 将此文件重命名为 config.ini
# 2. 填写您的用户名和密码
# 3. 选择合适的角色类型:
#    - admin: 管理员模式，可以批量处理所有教师和学生
#    - teacher: 教师模式，处理教师课程和其班级的学生
#    - student: 学生模式，只处理当前学生的课程
"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(example_content)
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        try:
            self.__post_init__()
            return True
        except ValueError:
            return False
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'username': self.username,
            'password': self.password,
            'role': self.role,
            'special_id': self.special_id,
            'start_from_teacher': self.start_from_teacher,
            'resume_from_breakpoint': self.resume_from_breakpoint,
            'default_password': self.default_password
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Config':
        """从字典创建配置对象"""
        return cls(
            username=data.get('username', ''),
            password=data.get('password', ''),
            role=data.get('role', 'student'),
            special_id=data.get('special_id', '1178'),
            start_from_teacher=data.get('start_from_teacher', ''),
            resume_from_breakpoint=data.get('resume_from_breakpoint', False),
            default_password=data.get('default_password', 'WCLhqxx2022')
        )
