# 安全教育平台自动化工具 - Makefile
# 提供便捷的项目管理命令

.PHONY: help install install-dev setup web start test format lint clean build

# 默认目标
help:
	@echo "安全教育平台自动化工具 - 可用命令："
	@echo ""
	@echo "  setup        - 初始化项目环境"
	@echo "  install      - 安装项目依赖"
	@echo "  install-dev  - 安装开发依赖"
	@echo "  web          - 启动Web界面"
	@echo "  start        - 启动命令行版本"
	@echo "  test         - 运行测试"
	@echo "  format       - 格式化代码"
	@echo "  lint         - 检查代码质量"
	@echo "  clean        - 清理临时文件"
	@echo "  build        - 构建项目"
	@echo ""
	@echo "使用方法: make <命令>"

# 项目初始化
setup:
	@echo "🚀 初始化项目环境..."
	python setup.py

# 安装依赖
install:
	@echo "📦 安装项目依赖..."
	pdm install

# 安装开发依赖
install-dev:
	@echo "📦 安装开发依赖..."
	pdm install -G dev

# 启动Web界面
web:
	@echo "🌐 启动Web界面..."
	pdm run web

# 启动命令行版本
start:
	@echo "⚡ 启动命令行版本..."
	pdm run start

# 运行测试
test:
	@echo "🧪 运行测试..."
	pdm run test

# 格式化代码
format:
	@echo "🎨 格式化代码..."
	pdm run format

# 检查代码质量
lint:
	@echo "🔍 检查代码质量..."
	pdm run lint

# 清理临时文件
clean:
	@echo "🧹 清理临时文件..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".coverage" -delete
	find . -type d -name "htmlcov" -exec rm -rf {} +
	find . -type d -name "dist" -exec rm -rf {} +
	find . -type d -name "build" -exec rm -rf {} +

# 构建项目
build:
	@echo "🔨 构建项目..."
	pdm build

# 快速启动（创建配置文件并启动Web界面）
quick-start:
	@echo "⚡ 快速启动..."
	@if [ ! -f config.ini ]; then \
		echo "📄 创建配置文件..."; \
		cp config.ini.example config.ini; \
		echo "⚠️  请编辑 config.ini 文件填写您的账号信息"; \
		echo "然后运行: make web"; \
	else \
		echo "🌐 启动Web界面..."; \
		pdm run web; \
	fi

# 检查环境
check:
	@echo "🔍 检查环境..."
	@python --version
	@pdm --version || echo "❌ PDM未安装，请运行: pip install pdm"
	@echo "📁 项目文件检查:"
	@ls -la pyproject.toml requirements.txt config.ini.example 2>/dev/null || echo "⚠️  某些文件可能缺失"
