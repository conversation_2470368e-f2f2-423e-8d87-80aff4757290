@echo off
chcp 65001 >nul
title 安全教育平台 - 打包工具选择

echo.
echo ========================================
echo   安全教育平台 - 打包工具选择
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

:menu
echo 请选择打包方式：
echo.
echo [1] 标准打包 - 基础可执行文件打包
echo     • 快速打包
echo     • 文件体积较小
echo     • 适合内部使用
echo.
echo [2] 高级加密打包 - 代码加密保护
echo     • 代码混淆加密
echo     • 防逆向工程
echo     • 适合外部分发
echo     • 可能被杀毒软件误报
echo.
echo [3] 查看现有打包结果
echo [4] 清理所有构建文件
echo [0] 退出
echo.
set /p choice=请输入选项 (0-4): 

if "%choice%"=="1" goto standard
if "%choice%"=="2" goto advanced
if "%choice%"=="3" goto view
if "%choice%"=="4" goto clean
if "%choice%"=="0" goto exit
echo 无效选项，请重新选择
goto menu

:standard
echo.
echo 📦 开始标准打包...
echo ========================================
echo.
python build_package.py
echo.
if %errorlevel% equ 0 (
    echo ✅ 标准打包完成！
    echo 📁 文件位置：dist_package 目录
) else (
    echo ❌ 标准打包失败！
)
echo.
pause
goto menu

:advanced
echo.
echo 🔒 开始高级加密打包...
echo ========================================
echo.
echo ⚠️ 注意事项：
echo   • 打包时间较长，请耐心等待
echo   • 生成的文件可能被杀毒软件误报
echo   • 需要用户以管理员身份运行
echo.
set /p confirm=确认继续？(y/N): 
if /i not "%confirm%"=="y" goto menu

python advanced_build.py
echo.
if %errorlevel% equ 0 (
    echo ✅ 高级加密打包完成！
    echo 📁 文件位置：dist_advanced 目录
    echo 🔒 程序已加密保护
) else (
    echo ❌ 高级加密打包失败！
)
echo.
pause
goto menu

:view
echo.
echo 📁 查看打包结果...
echo ========================================
echo.

if exist "dist_package" (
    echo 📦 标准打包结果：
    dir "dist_package" /b
    echo.
) else (
    echo ❌ 未找到标准打包结果
    echo.
)

if exist "dist_advanced" (
    echo 🔒 高级加密打包结果：
    dir "dist_advanced" /b
    echo.
) else (
    echo ❌ 未找到高级加密打包结果
    echo.
)

pause
goto menu

:clean
echo.
echo 🧹 清理构建文件...
echo ========================================
echo.

set /p confirm_clean=确认清理所有构建文件？(y/N): 
if /i not "%confirm_clean%"=="y" goto menu

echo 正在清理...

if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "dist_package" rmdir /s /q "dist_package"
if exist "dist_advanced" rmdir /s /q "dist_advanced"
if exist "build_package" rmdir /s /q "build_package"
if exist "advanced_build" rmdir /s /q "advanced_build"
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "*.spec" del /q "*.spec"

echo ✅ 清理完成！
echo.
pause
goto menu

:exit
echo.
echo 👋 感谢使用安全教育平台打包工具！
echo.
exit /b 0
