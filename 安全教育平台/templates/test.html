<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>功能测试页面</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>用户管理测试</h5>
                    </div>
                    <div class="card-body">
                        <form id="userForm">
                            <div class="mb-3">
                                <label class="form-label">用户名称</label>
                                <input type="text" class="form-control" id="userName" placeholder="请输入用户名称">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">登录账号</label>
                                <input type="text" class="form-control" id="userUsername" placeholder="请输入登录账号">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">登录密码</label>
                                <input type="text" class="form-control" id="userPassword" placeholder="请输入登录密码">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">角色类型</label>
                                <select class="form-select" id="userRole">
                                    <option value="student">学生</option>
                                    <option value="teacher">教师</option>
                                    <option value="admin">管理员</option>
                                </select>
                            </div>
                            <button type="button" class="btn btn-primary" id="saveUserBtn">
                                <i class="bi bi-save"></i> 保存用户
                            </button>
                            <button type="button" class="btn btn-secondary" id="resetUserFormBtn">
                                <i class="bi bi-arrow-clockwise"></i> 重置表单
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>已保存用户</h5>
                    </div>
                    <div class="card-body">
                        <div id="savedUsersList">
                            <div class="text-center text-muted">加载中...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>调试信息</h5>
                    </div>
                    <div class="card-body">
                        <div id="debugInfo" style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 调试函数
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            console.log(logMessage);
            
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.textContent += logMessage + '\n';
            debugInfo.scrollTop = debugInfo.scrollHeight;
        }
        
        // DOM元素
        const elements = {
            userName: document.getElementById('userName'),
            userUsername: document.getElementById('userUsername'),
            userPassword: document.getElementById('userPassword'),
            userRole: document.getElementById('userRole'),
            saveUserBtn: document.getElementById('saveUserBtn'),
            resetUserFormBtn: document.getElementById('resetUserFormBtn'),
            savedUsersList: document.getElementById('savedUsersList')
        };
        
        // 显示提示信息
        function showAlert(message, type = 'info') {
            log(`Alert: ${type} - ${message}`);
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }
        
        // 保存用户
        function saveUser() {
            log('saveUser函数被调用');
            
            try {
                const userData = {
                    name: elements.userName.value.trim(),
                    username: elements.userUsername.value.trim(),
                    password: elements.userPassword.value.trim(),
                    role: elements.userRole.value
                };

                log('用户数据: ' + JSON.stringify(userData));

                if (!userData.name || !userData.username || !userData.password) {
                    showAlert('请填写完整的用户信息', 'warning');
                    return;
                }

                // 禁用按钮防止重复提交
                elements.saveUserBtn.disabled = true;
                elements.saveUserBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 保存中...';

                log('发送POST请求到 /api/users');

                fetch('/api/users', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(userData)
                })
                .then(response => {
                    log(`响应状态: ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    log('响应数据: ' + JSON.stringify(data));
                    if (data.success) {
                        showAlert(data.message || '保存成功', 'success');
                        resetUserForm();
                        loadUsers();
                    } else {
                        showAlert(data.message || '保存失败', 'danger');
                    }
                })
                .catch(error => {
                    log('请求失败: ' + error.toString());
                    showAlert('网络错误，保存用户失败', 'danger');
                })
                .finally(() => {
                    // 恢复按钮状态
                    elements.saveUserBtn.disabled = false;
                    elements.saveUserBtn.innerHTML = '<i class="bi bi-save"></i> 保存用户';
                });
            } catch (error) {
                log('saveUser函数执行失败: ' + error.toString());
                showAlert('保存用户时发生错误', 'danger');
            }
        }
        
        // 重置表单
        function resetUserForm() {
            log('重置用户表单');
            elements.userName.value = '';
            elements.userUsername.value = '';
            elements.userPassword.value = '';
            elements.userRole.value = 'student';
        }
        
        // 加载用户列表
        function loadUsers() {
            log('加载用户列表');
            
            fetch('/api/users')
            .then(response => {
                log(`获取用户列表响应状态: ${response.status}`);
                return response.json();
            })
            .then(data => {
                log('用户列表数据: ' + JSON.stringify(data));
                if (data.success) {
                    displayUsers(data.users);
                } else {
                    elements.savedUsersList.innerHTML = '<div class="text-center text-muted">加载用户失败</div>';
                }
            })
            .catch(error => {
                log('加载用户列表失败: ' + error.toString());
                elements.savedUsersList.innerHTML = '<div class="text-center text-muted">加载用户失败</div>';
            });
        }
        
        // 显示用户列表
        function displayUsers(users) {
            log(`显示 ${users.length} 个用户`);
            
            if (users.length === 0) {
                elements.savedUsersList.innerHTML = '<div class="text-center text-muted">暂无保存的用户</div>';
                return;
            }
            
            const userHtml = users.map(user => `
                <div class="list-group-item d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <strong>${user.name}</strong> <small class="text-muted">(${user.username})</small>
                        <br>
                        <span class="badge bg-primary">${user.role}</span>
                        <small class="text-muted">${user.created_at}</small>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(${user.id}, '${user.name}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `).join('');
            
            elements.savedUsersList.innerHTML = userHtml;
        }
        
        // 删除用户
        function deleteUser(userId, userName) {
            if (!confirm(`确定要删除用户 "${userName}" 吗？`)) return;
            
            log(`删除用户: ${userId} - ${userName}`);
            
            fetch(`/api/users/${userId}`, { method: 'DELETE' })
            .then(response => response.json())
            .then(data => {
                log('删除用户响应: ' + JSON.stringify(data));
                if (data.success) {
                    showAlert(data.message, 'success');
                    loadUsers();
                } else {
                    showAlert(data.message, 'danger');
                }
            })
            .catch(error => {
                log('删除用户失败: ' + error.toString());
                showAlert('删除用户失败', 'danger');
            });
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始初始化');
            
            // 检查DOM元素
            for (const [name, element] of Object.entries(elements)) {
                if (!element) {
                    log(`错误: 元素 ${name} 未找到`);
                } else {
                    log(`成功: 元素 ${name} 已找到`);
                }
            }
            
            // 绑定事件
            if (elements.saveUserBtn) {
                elements.saveUserBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    log('保存用户按钮点击事件触发');
                    saveUser();
                });
                log('保存用户按钮事件绑定成功');
            }
            
            if (elements.resetUserFormBtn) {
                elements.resetUserFormBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    log('重置表单按钮点击事件触发');
                    resetUserForm();
                });
                log('重置表单按钮事件绑定成功');
            }
            
            // 加载初始数据
            loadUsers();
            
            log('初始化完成');
        });
    </script>
</body>
</html>
