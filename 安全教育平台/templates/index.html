<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全教育平台自动化工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            margin-bottom: 20px;
        }
        
        .card-header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
            padding: 15px 20px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 500;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .progress {
            height: 25px;
            border-radius: 15px;
            background-color: #f8f9fa;
        }
        
        .progress-bar {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 15px;
            transition: width 0.6s ease;
        }
        
        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.9rem;
        }
        
        .status-idle {
            background: #e9ecef;
            color: #6c757d;
        }
        
        .status-running {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .status-completed {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        
        .status-error {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 15px;
            color: #667eea;
        }
        
        .feature-card {
            text-align: center;
            padding: 30px 20px;
            height: 100%;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- 头部 -->
            <div class="header">
                <h1><i class="bi bi-shield-check"></i> 安全教育平台</h1>
                <p>自动化学习工具 - 让学习更简单高效</p>
            </div>
            
            <!-- 主要内容 -->
            <div class="content">
                <!-- 标签页导航 -->
                <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="task-tab" data-bs-toggle="tab" data-bs-target="#task-pane" type="button" role="tab" aria-controls="task-pane" aria-selected="true">
                            <i class="bi bi-play-circle"></i> 任务执行
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users-pane" type="button" role="tab" aria-controls="users-pane" aria-selected="false">
                            <i class="bi bi-people"></i> 用户管理
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="teachers-tab" data-bs-toggle="tab" data-bs-target="#teachers-pane" type="button" role="tab" aria-controls="teachers-pane" aria-selected="false">
                            <i class="bi bi-person-workspace"></i> 教师管理
                        </button>
                    </li>
                </ul>

                <!-- 标签页内容 -->
                <div class="tab-content" id="mainTabContent">
                    <!-- 任务执行标签页 -->
                    <div class="tab-pane fade show active" id="task-pane" role="tabpanel" aria-labelledby="task-tab">
                        <div class="row">
                            <!-- 左侧配置区域 -->
                            <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-gear"></i> 配置设置</h5>
                            </div>
                            <div class="card-body">
                                <form id="configForm">
                                    <div class="mb-3">
                                        <label class="form-label">快速选择用户</label>
                                        <select class="form-select" id="savedUserSelect">
                                            <option value="">手动输入或选择已保存的用户</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">用户名</label>
                                        <input type="text" class="form-control" id="username" placeholder="请输入用户名">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">密码</label>
                                        <input type="text" class="form-control" id="password" placeholder="请输入密码">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">角色类型</label>
                                        <select class="form-select" id="role">
                                            <option value="student">学生</option>
                                            <option value="teacher">教师</option>
                                            <option value="admin">管理员</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">项目ID (special_id)</label>
                                        <input type="text" class="form-control" id="specialId" placeholder="请输入项目ID，如：1178" value="1178">
                                        <div class="form-text">不同的项目对应不同的ID，默认为1178</div>
                                    </div>
                                    <div class="mb-3" id="onlyTeacherOption" style="display: none;">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="onlyTeacher">
                                            <label class="form-check-label" for="onlyTeacher">
                                                只处理教师（不处理学生）
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3" id="startFromTeacherOption" style="display: none;">
                                        <label class="form-label">从指定教师开始</label>
                                        <div class="position-relative">
                                            <input type="text" class="form-control" id="startFromTeacher" placeholder="搜索教师用户名或姓名，留空从头开始" autocomplete="off">
                                            <div class="dropdown-menu w-100" id="teacherDropdown" style="max-height: 200px; overflow-y: auto;">
                                                <!-- 教师选项将动态填充 -->
                                            </div>
                                        </div>
                                        <div class="form-text">管理员模式：从指定教师开始执行，跳过前面的教师</div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="resumeFromBreakpoint">
                                            <label class="form-check-label" for="resumeFromBreakpoint">
                                                断点续传（从上次中断的地方继续）
                                            </label>
                                        </div>
                                        <div class="form-text">启用后将跳过已完成的任务，继续未完成的部分</div>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-primary" id="saveConfigBtn">
                                            <i class="bi bi-save"></i> 保存配置
                                        </button>
                                        <button type="button" class="btn btn-success" id="startBtn">
                                            <i class="bi bi-play-fill"></i> 开始学习
                                        </button>
                                        <button type="button" class="btn btn-danger" id="stopBtn" style="display: none;">
                                            <i class="bi bi-stop-fill"></i> 停止任务
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                    </div>
                    
                    <!-- 右侧状态区域 -->
                    <div class="col-lg-8">
                        <!-- 任务状态 -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-activity"></i> 任务状态</h5>
                            </div>
                            <div class="card-body">
                                <div class="row align-items-center mb-3">
                                    <div class="col-auto">
                                        <span class="status-badge status-idle" id="statusBadge">待机中</span>
                                    </div>
                                    <div class="col">
                                        <span id="currentTask">等待开始...</span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>进度</span>
                                        <span id="progressText">0/0</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                                    </div>
                                </div>
                                
                                <div class="row text-center">
                                    <div class="col-4">
                                        <h6 class="text-muted mb-1">开始时间</h6>
                                        <span id="startTime">-</span>
                                    </div>
                                    <div class="col-4">
                                        <h6 class="text-muted mb-1">运行时长</h6>
                                        <span id="duration">-</span>
                                    </div>
                                    <div class="col-4">
                                        <h6 class="text-muted mb-1">完成时间</h6>
                                        <span id="endTime">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 实时日志 -->
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="bi bi-terminal"></i> 实时日志</h5>
                                <button class="btn btn-sm btn-outline-light" id="clearLogsBtn">
                                    <i class="bi bi-trash"></i> 清空
                                </button>
                            </div>
                            <div class="card-body p-0">
                                <div class="log-container" id="logContainer">
                                    <div>等待任务开始...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户管理标签页 -->
                <div class="tab-pane fade" id="users-pane" role="tabpanel" aria-labelledby="users-tab">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-person-plus"></i> 用户管理</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- 用户表单 -->
                                <div class="col-lg-6">
                                    <form id="userForm">
                                        <input type="hidden" id="userId">
                                        <div class="mb-3">
                                            <label class="form-label">用户名称</label>
                                            <input type="text" class="form-control" id="userName" placeholder="请输入用户名称">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">登录账号</label>
                                            <input type="text" class="form-control" id="userUsername" placeholder="请输入登录账号">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">登录密码</label>
                                            <input type="text" class="form-control" id="userPassword" placeholder="请输入登录密码">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">角色类型</label>
                                            <select class="form-select" id="userRole">
                                                <option value="student">学生</option>
                                                <option value="teacher">教师</option>
                                                <option value="admin">管理员</option>
                                            </select>
                                        </div>
                                        <div class="d-grid gap-2">
                                            <button type="button" class="btn btn-primary" id="saveUserBtn">
                                                <i class="bi bi-save"></i> 保存用户
                                            </button>
                                            <button type="button" class="btn btn-secondary" id="resetUserFormBtn">
                                                <i class="bi bi-arrow-clockwise"></i> 重置表单
                                            </button>
                                        </div>
                                    </form>
                                </div>

                                <!-- 已保存用户列表 -->
                                <div class="col-lg-6">
                                    <h6><i class="bi bi-people"></i> 已保存用户</h6>
                                    <div id="savedUsersList" class="list-group">
                                        <div class="list-group-item text-center text-muted">暂无保存的用户</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 教师管理标签页 -->
                <div class="tab-pane fade" id="teachers-pane" role="tabpanel" aria-labelledby="teachers-tab">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-person-workspace"></i> 教师管理</h5>
                        </div>
                        <div class="card-body">
                            <!-- 管理员选择 -->
                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <label class="form-label">选择管理员</label>
                                    <select class="form-select" id="adminSelect">
                                        <option value="">手动输入管理员信息</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">管理员账号</label>
                                    <input type="text" class="form-control" id="adminUsername" placeholder="请输入管理员账号">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">管理员密码</label>
                                    <input type="text" class="form-control" id="adminPassword" placeholder="请输入管理员密码">
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-primary w-100" id="fetchTeachersBtn">
                                        <i class="bi bi-download"></i> 获取教师列表
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-outline-secondary w-100" id="clearTeachersBtn">
                                        <i class="bi bi-trash"></i> 清空列表
                                    </button>
                                </div>
                            </div>

                            <!-- 多选教师功能 -->
                            <div class="mb-4">
                                <h6><i class="bi bi-check2-square"></i> 多选教师执行</h6>
                                <div class="row">
                                    <div class="col-md-8">
                                        <input type="text" class="form-control" id="teacherSearchInput" placeholder="搜索教师用户名或姓名...">
                                    </div>
                                    <div class="col-md-4">
                                        <button type="button" class="btn btn-success w-100" id="executeSelectedBtn" disabled>
                                            <i class="bi bi-play-fill"></i> 执行选中教师 (<span id="selectedCount">0</span>)
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 教师列表 -->
                            <div class="mb-3 d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">教师列表 <span class="badge bg-primary" id="teacherCount">0</span></h6>
                                <button class="btn btn-sm btn-outline-primary" id="refreshTeachersBtn">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                            </div>

                            <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                <table class="table table-hover table-sm">
                                    <thead class="table-light sticky-top">
                                        <tr>
                                            <th width="50">
                                                <input type="checkbox" class="form-check-input" id="selectAllTeachers">
                                            </th>
                                            <th>用户名</th>
                                            <th>姓名</th>
                                            <th>学校</th>
                                            <th>班级/学生</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="teachersTableBody">
                                        <tr>
                                            <td colspan="6" class="text-center text-muted py-4">
                                                <i class="bi bi-info-circle"></i> 请先使用管理员账号获取教师列表
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <script>
        // WebSocket连接
        const socket = io();
        
        // DOM元素
        const elements = {
            // 任务执行相关
            username: document.getElementById('username'),
            password: document.getElementById('password'),
            role: document.getElementById('role'),
            specialId: document.getElementById('specialId'),
            savedUserSelect: document.getElementById('savedUserSelect'),
            onlyTeacher: document.getElementById('onlyTeacher'),
            onlyTeacherOption: document.getElementById('onlyTeacherOption'),
            startFromTeacher: document.getElementById('startFromTeacher'),
            startFromTeacherOption: document.getElementById('startFromTeacherOption'),
            resumeFromBreakpoint: document.getElementById('resumeFromBreakpoint'),
            saveConfigBtn: document.getElementById('saveConfigBtn'),
            startBtn: document.getElementById('startBtn'),
            stopBtn: document.getElementById('stopBtn'),
            statusBadge: document.getElementById('statusBadge'),
            currentTask: document.getElementById('currentTask'),
            progressBar: document.getElementById('progressBar'),
            progressText: document.getElementById('progressText'),
            startTime: document.getElementById('startTime'),
            duration: document.getElementById('duration'),
            endTime: document.getElementById('endTime'),
            logContainer: document.getElementById('logContainer'),
            clearLogsBtn: document.getElementById('clearLogsBtn'),

            // 用户管理相关
            userId: document.getElementById('userId'),
            userName: document.getElementById('userName'),
            userUsername: document.getElementById('userUsername'),
            userPassword: document.getElementById('userPassword'),
            userRole: document.getElementById('userRole'),
            saveUserBtn: document.getElementById('saveUserBtn'),
            resetUserFormBtn: document.getElementById('resetUserFormBtn'),
            savedUsersList: document.getElementById('savedUsersList'),

            // 教师管理相关
            adminSelect: document.getElementById('adminSelect'),
            adminUsername: document.getElementById('adminUsername'),
            adminPassword: document.getElementById('adminPassword'),
            fetchTeachersBtn: document.getElementById('fetchTeachersBtn'),
            clearTeachersBtn: document.getElementById('clearTeachersBtn'),
            refreshTeachersBtn: document.getElementById('refreshTeachersBtn'),
            teacherSearchInput: document.getElementById('teacherSearchInput'),
            teachersTableBody: document.getElementById('teachersTableBody'),
            teacherCount: document.getElementById('teacherCount'),
            teacherDropdown: document.getElementById('teacherDropdown'),
            selectAllTeachers: document.getElementById('selectAllTeachers'),
            executeSelectedBtn: document.getElementById('executeSelectedBtn'),
            selectedCount: document.getElementById('selectedCount')
        };
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadConfig();
            updateRoleOptions();
            loadHistoryLogs();
            loadUsers();
            loadSavedUsers();
            initUserManagement();
            initTeacherManagement();
            updateAdminSelect();
        });
        
        // 角色选择变化
        elements.role.addEventListener('change', updateRoleOptions);
        
        function updateRoleOptions() {
            const role = elements.role.value;
            elements.onlyTeacherOption.style.display =
                (role === 'admin' || role === 'teacher') ? 'block' : 'none';
            elements.startFromTeacherOption.style.display =
                (role === 'admin') ? 'block' : 'none';
        }
        
        // 加载配置
        function loadConfig() {
            fetch('/api/config')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        elements.username.value = data.data.username || '';
                        elements.password.value = data.data.password || '';
                        elements.role.value = data.data.role || 'student';
                        elements.specialId.value = data.data.special_id || '1178';
                        elements.startFromTeacher.value = data.data.start_from_teacher || '';
                        elements.resumeFromBreakpoint.checked = data.data.resume_from_breakpoint || false;
                        updateRoleOptions();
                    }
                })
                .catch(error => console.error('加载配置失败:', error));
        }

        // 加载历史日志
        function loadHistoryLogs() {
            fetch('/api/logs')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.logs.length > 0) {
                        elements.logContainer.innerHTML = '';
                        data.logs.forEach(log => {
                            addLogMessage(log);
                        });
                    }
                })
                .catch(error => console.error('加载历史日志失败:', error));
        }

        // 保存配置
        elements.saveConfigBtn.addEventListener('click', function() {
            const config = {
                username: elements.username.value.trim(),
                password: elements.password.value.trim(),
                role: elements.role.value,
                special_id: elements.specialId.value.trim() || '1178',
                start_from_teacher: elements.startFromTeacher.value.trim(),
                resume_from_breakpoint: elements.resumeFromBreakpoint.checked
            };
            
            if (!config.username || !config.password) {
                showAlert('请填写完整的用户名和密码', 'warning');
                return;
            }
            
            fetch('/api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('配置保存成功', 'success');
                } else {
                    showAlert(data.message, 'danger');
                }
            })
            .catch(error => {
                showAlert('保存配置失败', 'danger');
                console.error('Error:', error);
            });
        });
        
        // 开始任务
        elements.startBtn.addEventListener('click', function() {
            const config = {
                username: elements.username.value.trim(),
                password: elements.password.value.trim(),
                role: elements.role.value,
                special_id: elements.specialId.value.trim() || '1178',
                only_teacher: elements.onlyTeacher.checked,
                start_from_teacher: elements.startFromTeacher.value.trim(),
                resume_from_breakpoint: elements.resumeFromBreakpoint.checked
            };
            
            if (!config.username || !config.password) {
                showAlert('请填写完整的用户名和密码', 'warning');
                return;
            }
            
            fetch('/api/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('任务已启动', 'success');
                    elements.startBtn.style.display = 'none';
                    elements.stopBtn.style.display = 'block';
                } else {
                    showAlert(data.message, 'danger');
                }
            })
            .catch(error => {
                showAlert('启动任务失败', 'danger');
                console.error('Error:', error);
            });
        });
        
        // 停止任务
        elements.stopBtn.addEventListener('click', function() {
            fetch('/api/stop', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('任务已停止', 'info');
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        });
        
        // 清空日志
        elements.clearLogsBtn.addEventListener('click', function() {
            elements.logContainer.innerHTML = '<div>日志已清空...</div>';
        });
        
        // WebSocket事件处理
        socket.on('status_update', function(status) {
            updateStatus(status);
        });
        
        socket.on('log_update', function(data) {
            addLogMessage(data.message);
        });
        
        // 更新状态
        function updateStatus(status) {
            // 更新状态徽章
            const badge = elements.statusBadge;
            badge.className = 'status-badge ';
            
            if (status.running) {
                badge.className += 'status-running pulse';
                badge.textContent = '运行中';
                elements.startBtn.style.display = 'none';
                elements.stopBtn.style.display = 'block';
            } else if (status.end_time) {
                if (status.current_task.includes('失败') || status.current_task.includes('错误')) {
                    badge.className += 'status-error';
                    badge.textContent = '执行失败';
                } else {
                    badge.className += 'status-completed';
                    badge.textContent = '已完成';
                }
                elements.startBtn.style.display = 'block';
                elements.stopBtn.style.display = 'none';
            } else {
                badge.className += 'status-idle';
                badge.textContent = '待机中';
                elements.startBtn.style.display = 'block';
                elements.stopBtn.style.display = 'none';
            }
            
            // 更新当前任务
            elements.currentTask.textContent = status.current_task || '等待开始...';
            
            // 更新进度
            const progress = status.total > 0 ? (status.progress / status.total) * 100 : 0;
            elements.progressBar.style.width = progress + '%';
            elements.progressText.textContent = `${status.progress}/${status.total}`;
            
            // 更新时间
            elements.startTime.textContent = status.start_time || '-';
            elements.endTime.textContent = status.end_time || '-';

            // 更新运行时长
            if (status.start_time && status.running) {
                // 如果任务正在运行，计算实时运行时长
                updateDuration(status.start_time);
            } else if (status.start_time && status.end_time) {
                // 如果任务已完成，显示总运行时长
                const duration = calculateDuration(status.start_time, status.end_time);
                elements.duration.textContent = duration;
            } else {
                elements.duration.textContent = '-';
            }
        }

        // 计算运行时长
        function calculateDuration(startTime, endTime) {
            const start = parseTime(startTime);
            const end = parseTime(endTime);
            const duration = Math.floor((end - start) / 1000);
            const hours = Math.floor(duration / 3600);
            const minutes = Math.floor((duration % 3600) / 60);
            const seconds = duration % 60;

            if (hours > 0) {
                return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            } else {
                return `${minutes}:${seconds.toString().padStart(2, '0')}`;
            }
        }

        // 解析时间字符串
        function parseTime(timeStr) {
            if (!timeStr) return new Date();

            // 如果是 HH:MM:SS 格式，转换为今天的时间
            if (timeStr.match(/^\d{2}:\d{2}:\d{2}$/)) {
                const today = new Date();
                const [hours, minutes, seconds] = timeStr.split(':').map(Number);
                return new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes, seconds);
            }

            return new Date(timeStr);
        }

        // 更新实时运行时长
        function updateDuration(startTime) {
            const start = parseTime(startTime);
            const now = new Date();
            const duration = Math.floor((now - start) / 1000);
            const hours = Math.floor(duration / 3600);
            const minutes = Math.floor((duration % 3600) / 60);
            const seconds = duration % 60;

            if (hours > 0) {
                elements.duration.textContent = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            } else {
                elements.duration.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            }
        }

        // 添加日志消息
        function addLogMessage(message) {
            const logDiv = document.createElement('div');
            logDiv.textContent = message;
            elements.logContainer.appendChild(logDiv);
            elements.logContainer.scrollTop = elements.logContainer.scrollHeight;
        }
        
        // 显示提示消息
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
        
        // 定期更新状态
        setInterval(() => {
            fetch('/api/status')
                .then(response => response.json())
                .then(status => updateStatus(status))
                .catch(error => console.error('获取状态失败:', error));
        }, 2000);

        // ===== 用户管理功能 =====

        // 初始化用户管理
        function initUserManagement() {
            // 保存用户按钮
            elements.saveUserBtn.addEventListener('click', saveUser);

            // 重置表单按钮
            elements.resetUserFormBtn.addEventListener('click', resetUserForm);

            // 快速选择用户下拉框
            elements.savedUserSelect.addEventListener('change', function() {
                const userId = this.value;
                if (userId) {
                    loadUserToForm(parseInt(userId));
                }
            });
        }

        // 加载用户列表
        function loadUsers() {
            fetch('/api/users')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayUsers(data.users);
                    } else {
                        showAlert('加载用户列表失败', 'danger');
                    }
                })
                .catch(error => {
                    console.error('加载用户列表失败:', error);
                    showAlert('加载用户列表失败', 'danger');
                });
        }

        // 全局教师列表存储
        let teachersList = [];

        // 更新教师搜索下拉框
        function updateTeacherDropdown(teachers, searchTerm = '') {
            const dropdown = elements.teacherDropdown;
            dropdown.innerHTML = '';

            // 过滤教师列表
            const filteredTeachers = teachers.filter(teacher => {
                const searchLower = searchTerm.toLowerCase();
                return teacher.username.toLowerCase().includes(searchLower) ||
                       (teacher.nickname && teacher.nickname.toLowerCase().includes(searchLower));
            });

            if (filteredTeachers.length === 0) {
                dropdown.innerHTML = '<div class="dropdown-item-text text-muted">无匹配结果</div>';
                return;
            }

            filteredTeachers.forEach(teacher => {
                const item = document.createElement('div');
                item.className = 'dropdown-item';
                item.style.cursor = 'pointer';
                item.textContent = `${teacher.username} (${teacher.nickname || '未知'})`;
                item.onclick = () => {
                    elements.startFromTeacher.value = teacher.username;
                    dropdown.classList.remove('show');
                };
                dropdown.appendChild(item);
            });
        }

        // 加载已保存的用户到快速选择下拉框
        function loadSavedUsers() {
            fetch('/api/users')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateSavedUserSelect(data.users);
                    }
                })
                .catch(error => console.error('加载已保存用户失败:', error));
        }

        // 更新快速选择用户下拉框
        function updateSavedUserSelect(users) {
            const select = elements.savedUserSelect;
            select.innerHTML = '<option value="">手动输入或选择已保存的用户</option>';

            users.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.name} (${user.username}) - ${user.role}`;
                select.appendChild(option);
            });
        }

        // 将用户信息加载到任务执行表单
        function loadUserToForm(userId) {
            fetch('/api/users')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const user = data.users.find(u => u.id === userId);
                        if (user) {
                            elements.username.value = user.username;
                            elements.password.value = user.password;
                            elements.role.value = user.role;
                            // 项目ID保持任务执行页面的当前值，不从用户数据中覆盖
                            updateRoleOptions();
                        }
                    }
                })
                .catch(error => console.error('加载用户信息失败:', error));
        }

        // 显示用户列表（简化版）
        function displayUsers(users) {
            const container = elements.savedUsersList;

            if (users.length === 0) {
                container.innerHTML = '<div class="list-group-item text-center text-muted">暂无保存的用户</div>';
                return;
            }

            container.innerHTML = users.map(user => `
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${user.name}</strong> <small class="text-muted">(${user.username})</small>
                        <br>
                        <span class="badge bg-${getRoleBadgeColor(user.role)} me-1">${getRoleDisplayName(user.role)}</span>
                        <small class="text-muted">${user.created_at}</small>
                    </div>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editUser(${user.id})" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteUser(${user.id}, '${user.name}')" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');

            // 更新快速选择下拉框
            updateSavedUserSelect(users);
        }

        // 获取角色徽章颜色
        function getRoleBadgeColor(role) {
            switch(role) {
                case 'admin': return 'danger';
                case 'teacher': return 'warning';
                case 'student': return 'primary';
                default: return 'secondary';
            }
        }

        // 获取角色显示名称
        function getRoleDisplayName(role) {
            switch(role) {
                case 'admin': return '管理员';
                case 'teacher': return '教师';
                case 'student': return '学生';
                default: return '未知';
            }
        }

        // 保存用户
        function saveUser() {
            const userData = {
                id: elements.userId.value ? parseInt(elements.userId.value) : null,
                name: elements.userName.value.trim(),
                username: elements.userUsername.value.trim(),
                password: elements.userPassword.value.trim(),
                role: elements.userRole.value
            };

            if (!userData.name || !userData.username || !userData.password) {
                showAlert('请填写完整的用户信息', 'warning');
                return;
            }

            fetch('/api/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message, 'success');
                    resetUserForm();
                    loadUsers();
                    updateAdminSelect(); // 更新管理员下拉框
                } else {
                    showAlert(data.message, 'danger');
                }
            })
            .catch(error => {
                showAlert('保存用户失败', 'danger');
                console.error('Error:', error);
            });
        }

        // 编辑用户
        function editUser(userId) {
            fetch('/api/users')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const user = data.users.find(u => u.id === userId);
                        if (user) {
                            elements.userId.value = user.id;
                            elements.userName.value = user.name;
                            elements.userUsername.value = user.username;
                            elements.userPassword.value = user.password;
                            elements.userRole.value = user.role;

                            // 切换到用户管理标签页
                            document.getElementById('users-tab').click();
                        }
                    }
                })
                .catch(error => console.error('加载用户信息失败:', error));
        }

        // 删除用户
        function deleteUser(userId, userName) {
            if (confirm(`确定要删除用户 "${userName}" 吗？`)) {
                fetch(`/api/users?id=${userId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert(data.message, 'success');
                        loadUsers();
                        updateAdminSelect(); // 更新管理员下拉框
                    } else {
                        showAlert(data.message, 'danger');
                    }
                })
                .catch(error => {
                    showAlert('删除用户失败', 'danger');
                    console.error('Error:', error);
                });
            }
        }

        // 重置用户表单
        function resetUserForm() {
            elements.userId.value = '';
            elements.userName.value = '';
            elements.userUsername.value = '';
            elements.userPassword.value = '';
            elements.userRole.value = 'student';
        }

        // ===== 教师管理功能 =====

        // 全局变量
        let selectedTeachers = [];

        // 初始化教师管理
        function initTeacherManagement() {
            // 管理员选择下拉框
            elements.adminSelect.addEventListener('change', function() {
                const selectedValue = this.value;
                if (selectedValue) {
                    const [username, password] = selectedValue.split('|');
                    elements.adminUsername.value = username;
                    elements.adminPassword.value = password;
                } else {
                    elements.adminUsername.value = '';
                    elements.adminPassword.value = '';
                }
            });

            // 获取教师列表按钮
            elements.fetchTeachersBtn.addEventListener('click', fetchTeachersList);

            // 清空教师列表按钮
            elements.clearTeachersBtn.addEventListener('click', clearTeachersList);

            // 刷新教师列表按钮
            elements.refreshTeachersBtn.addEventListener('click', refreshTeachersList);

            // 教师搜索输入框
            elements.teacherSearchInput.addEventListener('input', function() {
                const searchTerm = this.value;
                filterTeachersTable(searchTerm);
            });

            // 全选教师复选框
            elements.selectAllTeachers.addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('input[name="teacherSelect"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateSelectedTeachers();
            });

            // 执行选中教师按钮
            elements.executeSelectedBtn.addEventListener('click', executeSelectedTeachers);

            // 从指定教师开始的搜索输入框
            if (elements.startFromTeacher) {
                elements.startFromTeacher.addEventListener('input', function() {
                    const searchTerm = this.value;
                    if (searchTerm.length > 0 && teachersList.length > 0) {
                        updateTeacherDropdown(teachersList, searchTerm);
                        elements.teacherDropdown.classList.add('show');
                    } else {
                        elements.teacherDropdown.classList.remove('show');
                    }
                });

                // 点击外部关闭下拉框
                document.addEventListener('click', function(e) {
                    if (!elements.startFromTeacher.contains(e.target) && !elements.teacherDropdown.contains(e.target)) {
                        elements.teacherDropdown.classList.remove('show');
                    }
                });
            }
        }

        // 获取教师列表
        function fetchTeachersList() {
            const adminUsername = elements.adminUsername.value.trim();
            const adminPassword = elements.adminPassword.value.trim();

            if (!adminUsername || !adminPassword) {
                showAlert('请填写管理员账号和密码', 'warning');
                return;
            }

            // 显示加载状态
            elements.fetchTeachersBtn.disabled = true;
            elements.fetchTeachersBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 获取中...';

            fetch('/api/teachers/fetch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: adminUsername,
                    password: adminPassword
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    teachersList = data.teachers;
                    displayTeachers(teachersList);
                    showAlert(`成功获取 ${teachersList.length} 个教师信息`, 'success');
                } else {
                    showAlert(data.message || '获取教师列表失败', 'danger');
                }
            })
            .catch(error => {
                console.error('获取教师列表失败:', error);
                showAlert('获取教师列表失败', 'danger');
            })
            .finally(() => {
                elements.fetchTeachersBtn.disabled = false;
                elements.fetchTeachersBtn.innerHTML = '<i class="bi bi-download"></i> 获取教师列表';
            });
        }

        // 清空教师列表
        function clearTeachersList() {
            if (confirm('确定要清空教师列表吗？')) {
                teachersList = [];
                displayTeachers([]);
                showAlert('教师列表已清空', 'info');
            }
        }

        // 刷新教师列表
        function refreshTeachersList() {
            if (teachersList.length === 0) {
                showAlert('请先获取教师列表', 'warning');
                return;
            }
            fetchTeachersList();
        }

        // 显示教师列表
        function displayTeachers(teachers) {
            const tbody = elements.teachersTableBody;
            elements.teacherCount.textContent = teachers.length;

            if (teachers.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted py-4"><i class="bi bi-info-circle"></i> 暂无教师数据</td></tr>';
                return;
            }

            tbody.innerHTML = teachers.map(teacher => `
                <tr>
                    <td>
                        <input type="checkbox" class="form-check-input" name="teacherSelect" value="${teacher.username}" onchange="updateSelectedTeachers()">
                    </td>
                    <td><code>${teacher.username}</code></td>
                    <td>${teacher.nickname || '未知'}</td>
                    <td>${teacher.school || '未知'}</td>
                    <td>
                        <span class="badge bg-info me-1">${teacher.class_count || 0}</span>
                        <span class="badge bg-success">${teacher.student_count || 0}</span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="selectTeacher('${teacher.username}')" title="选择为起始教师">
                            <i class="bi bi-check-circle"></i>
                        </button>
                    </td>
                </tr>
            `).join('');

            // 重置选择状态
            selectedTeachers = [];
            updateSelectedTeachers();
        }

        // 过滤教师表格
        function filterTeachersTable(searchTerm) {
            if (!searchTerm) {
                displayTeachers(teachersList);
                return;
            }

            const filteredTeachers = teachersList.filter(teacher => {
                const searchLower = searchTerm.toLowerCase();
                return teacher.username.toLowerCase().includes(searchLower) ||
                       (teacher.nickname && teacher.nickname.toLowerCase().includes(searchLower)) ||
                       (teacher.school && teacher.school.toLowerCase().includes(searchLower));
            });

            displayTeachers(filteredTeachers);
        }

        // 选择教师（用于从指定教师开始）
        function selectTeacher(username) {
            elements.startFromTeacher.value = username;
            // 切换到任务执行标签页
            document.getElementById('task-tab').click();
            showAlert(`已选择教师: ${username}`, 'success');
        }

        // 更新选中教师列表
        function updateSelectedTeachers() {
            const checkboxes = document.querySelectorAll('input[name="teacherSelect"]:checked');
            selectedTeachers = Array.from(checkboxes).map(cb => cb.value);

            elements.selectedCount.textContent = selectedTeachers.length;
            elements.executeSelectedBtn.disabled = selectedTeachers.length === 0;

            // 更新全选复选框状态
            const allCheckboxes = document.querySelectorAll('input[name="teacherSelect"]');
            if (allCheckboxes.length > 0) {
                elements.selectAllTeachers.checked = selectedTeachers.length === allCheckboxes.length;
                elements.selectAllTeachers.indeterminate = selectedTeachers.length > 0 && selectedTeachers.length < allCheckboxes.length;
            }
        }

        // 执行选中的教师
        function executeSelectedTeachers() {
            if (selectedTeachers.length === 0) {
                showAlert('请先选择要执行的教师', 'warning');
                return;
            }

            if (confirm(`确定要对选中的 ${selectedTeachers.length} 个教师执行学习任务吗？`)) {
                // 切换到任务执行标签页
                document.getElementById('task-tab').click();

                // 这里可以添加批量执行的逻辑
                // 暂时显示选中的教师列表
                const teacherList = selectedTeachers.join(', ');
                showAlert(`将执行以下教师的学习任务: ${teacherList}`, 'info');

                // TODO: 实现批量执行API
                // 可以调用新的API端点 /api/execute-teachers
                // 传递选中的教师用户名列表
            }
        }

        // 更新管理员选择下拉框
        function updateAdminSelect() {
            fetch('/api/users')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const adminUsers = data.users.filter(user => user.role === 'admin');
                        const select = elements.adminSelect;

                        // 清空现有选项，保留默认选项
                        select.innerHTML = '<option value="">手动输入管理员信息</option>';

                        adminUsers.forEach(admin => {
                            const option = document.createElement('option');
                            option.value = `${admin.username}|${admin.password}`;
                            option.textContent = `${admin.name} (${admin.username})`;
                            select.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('加载管理员列表失败:', error));
        }
    </script>
</body>
</html>
