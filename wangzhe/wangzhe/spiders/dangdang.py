import scrapy


class DangSpider(scrapy.Spider):
    name = 'dangdang'
    allowed_domains = ['dangdang.com']
    start_urls = 'http://search.dangdang.com/?key=%BF%DA%BA%EC&act=input&page_index={page}'

    def start_requests(self):
        for page in range(1, 5):
            yield scrapy.Request(self.start_urls.format(page=page), self.parse_name)

    def parse_name(self, response):
        for item in response.css('.shoplist li'):
            pass
            data = {
                'title': item.css('a::attr(title)').get(),
                'price': item.css('.price_n::text').get(),
                'num': item.css('.star a::text').get(),
                'link': item.css('a::attr(href)').get(),

            }
            yield data
