import requests
import time
from loguru import logger

session = requests.Session()

start_time = int(time.time() * 1000)
times = 0
num = 0
courseId = "2721171"


def login():
    url = "http://study.teacheredu.cn/proj/proj/tlogin/urlVerification.json"
    payload = "{UserName:'50a49a100a122a112a120a116a100a48a48a53',PassWord:'52a50a57a55a102a52a52a98a49a51a57a53a53a50a51a53a50a52a53a98a50a52a57a55a51a57a57a100a55a97a57a51'}"
    session.post(url, data=payload)


def init_msg(ts, vid='A4CEC473F0FD7AD39C33DC5901307461'):
    url = "http://logger.csslcloud.net/event/vod/v1/client"
    post_json = {
        "ua": "mozilla/5.0 (windows nt 10.0; win64; x64) applewebkit/537.36 (khtml, like gecko) chrome/96.0.4664.55 safari/537.36 edg/96.0.1054.34",
        "platform": "h5-pc", "uuid": "78877228", "rid": ts, "ver": "v1.0.6", "appver": "2.7.6",
        "business": "1001", "userid": "", "appid": "039C1380CF417F50", "event": "play",
        "vid": vid, "nodeip": "", "retry": 0, "et": 364, "code": 200,
        "cdn": "cm15-c110-2.play.bokecc.com"}
    resp = session.post(url, json=post_json, headers={
        'Proxy-Connection': 'keep-alive',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.55 Safari/537.36 Edg/96.0.1054.34',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': '*/*',
        'Origin': 'http://course.teacheredu.cn',
        'Referer': 'http://course.teacheredu.cn/',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6'
    })
    logger.info(resp.headers)


def send_msg(ts, num=1, vid='A4CEC473F0FD7AD39C33DC5901307461'):
    url = "http://logger.csslcloud.net/event/vod/v1/client"

    post_json = {
        "ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.55 Safari/537.36 Edg/96.0.1054.34",
        "platform": "h5-pc", "uuid": "78877228",
        "rid": ts,
        "ver": "v1.0.7", "appver": "2.7.6",
        "business": "1001", "userid": "", "appid": "039C1380CF417F50", "event": "heartbeat",
        "vid": vid,
        "retry": 0, "code": 200, "cdn": "cm15-c110-2.play.bokecc.com",
        "heartinter": 60,
        "num": num,
        "playerstatus": 0, "blocktimes": 0, "blockduration": 0}
    resp = session.post(url, json=post_json, headers={
        'Proxy-Connection': 'keep-alive',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.55 Safari/537.36 Edg/96.0.1054.34',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': '*/*',
        'Origin': 'http://course.teacheredu.cn',
        'Referer': 'http://course.teacheredu.cn/',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6'
    })
    logger.info(resp.headers)


def send_times(total_time):
    url = f"http://study.teacheredu.cn/proj/studentwork/studyAjax/AddStudyTimeExit.json?time={total_time}"

    payload = f'courseId={courseId}&studyTime={total_time}'
    headers = {
        'Host': 'study.teacheredu.cn',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'X-Requested-With': 'XMLHttpRequest',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.55 Safari/537.36 Edg/96.0.1054.34',
        'Origin': 'http://study.teacheredu.cn',
        'Referer': f'http://study.teacheredu.cn/proj/studentwork/study.htm?courseId={courseId}&ptcode=34101&stageId=0',
        'Cookie': 'JSESSIONID=BF477C11853D65E6B2EFE6791F1B0716; JSESSION_ID=TLFK8Z-0KGVQHDH2FCUO9C1F73F3-X2IBHKWK-1FGF; _const_cas_assertion_id_=10930679; _const_cas_assertion_=21dzpxtd005; JSESSIONID=C4FC97AEE143EB19CD2C937495057EF3; tmp_uc0=eNpVzs0KgkAUBWCKgiiieoXWCnO94IzuSp0UKyud%2FjaD5ARBYWiLInr3lFbtzvk25%2Fhd4UhRqkIGWQeIhcSk1sBzhbyUsnyVssivqkH6tWyqKJPXXTXR6MdeHAfRUi4i15uT%2FfuS2eNkzkN21Ek426591ze4IyLLAU6Ro743gqkf7kId%2BIyPtdPDBhMZUEaRIKCWnn7AwKSMUUO7PW3yGf4NVydbyEwY1bq6po9zXtykk2eqHSfCPQz%2BPMga0KtlUqi0as%2FjF85uRVQ%3D; lastlogin="2021-11-29 17:39"; looyu_id=b2b4b2b0c55b429eb21105ef3f20d488_20003718%3A4; looyu_20003718=v%3A3ad871e789faaed0de6dd4e85d9e5383%2Cref%3A%2Cr%3A%2Cmon%3A//m6816.talk99.cn/monitor%2Cp0%3Ahttp%253A//study.teacheredu.cn/proj/studentwork/study.htm%253FcourseId%253D2721175%2526ptcode%253D34101%2526stageId%253D0; CAS_SID=222bd7f6-6af2-4cbf-9a3a-cbcb2eb1cf58:1638183296373; tmp_pc0=eNptUcFu00AQDWlaR5UKiPYLKvWWWGtvHNtRTthxszgkIU4g7WWxdxfVlWNH3k2Vtqoq8QeGD%2BitPaGKExI3bvxIlQ%2FghLhggyJxYE6jN%2FPm6c3r7g0tPEoihsfnc4ahCjTQ0B8pT3J4wlmKh2lyihFtox87Xsfz0KCPXw7sTg9ML0Pa2h%2F3HNc4rgP38PWrrt1VHWsyMC3F0aED61MVPe%2B6b9y64hw6%2BzUiWkoTGopu6FpD1YyaT%2F4CBlTNJtS12mzZAle7ufS8UJ14nRFGNnbZ%2BVEJrL48Ho4GL7CNrPH4aNjxLixwQGJZMJ%2BcsJTRhcyJTEMiZJrM%2FDCW7bwXuaubLRrE%2FoyVQ1rl8ygU%2BcEKSSirFGglTgTbjNgZizbixWyLC18suFSMij2epGIz8AU5kWiAYsqW1UKjF3IhsaVA8bvkrcQiXIDt6VWvVNqt%2F7ID%2FnTijXJzOlAN1YCmoZUfPr9ffb3rf%2Fx%2BmVX8lPlZP69%2FuNf%2F5zZAA5YfPt2u7rJ%2Bdp9JfBGcMiI%2BFOztdUqIVhVgQtDUzWfrQBFdx9n%2B%2BW17%2FdQ%2FUR7%2FBhNCpJw%3D; CAS_SID=222bd7f6-6af2-4cbf-9a3a-cbcb2eb1cf58:1638183613015; JSESSION_ID=TLFK8Z-AKGVKNTDZ99SI2TSPLBR2-4DXWJKWK-YMON1; tmp_pc0=eNrz4A12DQ729PeL9%2FV3cfUxiKzOTLFSCvFx87aI0nX0dg%2Fz9gtxibK0DPY0CgkO8HEKMtI1cYkI9%2FIO99aN9PX3M1TSSS6xMjQztjC0MDawsDQ0stBJTEYTyK2wMqiNAgBGfhy%2F',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    response = session.post(url, headers=headers, data=payload)

    print(response.text)


login()
init_msg(start_time)
for items in range(0, 5):
    times = start_time + items * 60 * 60
    num = items + 1
    send_msg(times, num)
    # time.sleep(1000)

total_time = int((times - start_time) / 60)
send_times(total_time)
