[project]
name = ""
version = ""
description = ""
authors = [
    {name = "moxiaoying", email = "<EMAIL>"},
]
dependencies = [
    "httpx>=0.23.0",
    "requests>=2.30.0",
    "beautifulsoup4>=4.11.1",
    "lxml>=4.9.1",
    "pandas>=1.4.3",
    "openpyxl>=3.0.10",
    "loguru>=0.6.0",
    "xlwt>=1.3.0",
    "m3u8>=3.3.0",
    "pycryptodome>=3.21.0",
    "bcrypt>=4.0.1",
    "pdfkit>=1.0.0",
    "wkhtmltopdf>=0.2",
    "reportlab>=4.0.0",
    "flask>=3.1.1",
    "flask-socketio>=5.5.1",
    "python-socketio>=5.13.0",
]
requires-python = ">=3.10"
license = {text = "MIT"}


[tool]
[tool.pdm]
[[tool.pdm.source]]
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
#url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"