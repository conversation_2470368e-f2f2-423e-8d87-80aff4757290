import time

import requests
import urllib3
from loguru import logger

urllib3.disable_warnings()


class User:
    def __init__(self, username, password):
        self.username = username
        self.password = password
        self.nickname = None
        self.session = requests.session()
        self.session.verify = False
        self.session.headers = {
            "Host": "api.ymjjz.com",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF XWEB/8447",
            "content-type": "application/json; charset=UTF-8",
            "referer": "https://servicewechat.com/wx06902710c7dedd7d/6/page-frame.html"
        }
        self.uid = None

    def login(self, username=None, password=None):
        """
        登陆并返回uid
        :param username:
        :param password:
        :return:
        """
        username = username or self.username
        password = password or self.password
        response = self.session.post('https://api.ymjjz.com/api/user/login',
                                     json={"phone": username, "pass": password})
        if response.status_code == 200:
            user = response.json()['user']
            logger.success(f'{user["nickname"]} 登录成功')
            self.uid = user['user_id']
            self.nickname = user['nickname']
            return user['user_id']
        return None


class Study(User):
    def get_movie_list(self):
        """
        获取视频列表
        :return:
        """
        url = "https://api.ymjjz.com/api/index/index"
        response = self.session.get(url)
        if response.status_code == 200:
            return response.json()['list']
        return None

    def get_study_score(self):
        """
        获取学习积分
        :return:
        """
        url = "https://api.ymjjz.com/api/user/ind"
        response = self.session.get(url, params={"uid": self.uid}).json()
        if response['sta']:
            return response['starCount']
        logger.error('获取学习积分失败')
        pass

    def get_movie_detail(self, vid):
        url = "https://api.ymjjz.com/api/Video/info"

        querystring = {"uid": self.uid, "vid": vid}
        response = self.session.get(url, params=querystring).json()
        if not response['sta']:
            logger.error('进入视频页面失败')
            return False

    def update_with_complete(self, movie_info):
        """
        更新视频直到结束
        :param movie_info:
        :return:
        """
        count = 1
        while True:
            status = self.update_study_process(movie_info)
            if not status:
                logger.success(f'{movie_info["title"]} 学习完成')
                return
            logger.info(f'{movie_info["title"]}已提交{count}次，休息0.1秒后继续')
            count += 1
            time.sleep(0.1)

    def update_study_process(self, movie_info):
        """
        更新学习进度
        :param movie_info:{
                            "article_id": 58,
                            "title": "\u8ddf\u603b\u4e66\u8bb0\u5b66\u515a\u53f21-\u4e00\u8258\u5c0f\u8239\u8bde\u751f\u4e00\u4e2a\u5927\u515a",
                            "description": "",
                            "thumb": "http:\/\/img.ymjjz.com\/\/public\/upload\/article\/2022\/08-18\/265b70385904c72277047584eca3382b.png",
                            "click": 114411,
                            "publish_time": 1660838400,
                            "laiyuan": "",
                            "fabu": "08-19"
                        }
        :return:
        """
        url = "https://api.ymjjz.com/api/Video/inmide"
        uid = self.uid or self.login()
        payload = {
            "uid": uid,
            "vid": movie_info['article_id'],
            "ti": 15
        }
        response = self.session.post(url, json=payload).json()
        if not response['sta']:
            logger.error('更新学习进度失败')
            return False
        return True

    def start(self):
        self.login()
        movie_list = self.get_movie_list()
        for cat_list in movie_list[0]['plist'][0:]:
            logger.info(f'开始学习章节:{cat_list["cat_name"]}')
            for movie_info in cat_list['plist']:
                logger.info(f'开始学习视频:{movie_info["title"]}')
                self.get_movie_detail(movie_info['article_id'])
                self.update_with_complete(movie_info)
                score = self.get_study_score()
                logger.info(f'学习分数:{score}')
                if score >= 20:
                    logger.success(f'分数已达20，学习完毕')
                    return
                logger.info(f'休息1秒后继续')
                time.sleep(1)


def main():
    pass


if __name__ == '__main__':
    study = Study('18523669053', '193594')
    study.start()
    # print(get_movie_list())
    # login('19923657345', '156059')
    pass
