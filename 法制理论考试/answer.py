# from peewee import *
from peewee import Model, Char<PERSON>ield, IntegerField, BooleanField, SqliteDatabase, MySQLDatabase

# db = MySQLDatabase(
#     'tiku',
#     host='**********',
#     user='tiku',
#     password='xhbPbhrY4dpeJJJY'
# )


# class Answer(Model):
#     title = CharField(primary_key=True)
#     answers = Char<PERSON>ield()
#     question_type = IntegerField()
#
#     class Meta:
#         database = db


db = SqliteDatabase('answer.db')


class Answer(Model):
    qid = Char<PERSON>ield(primary_key=True)
    title = CharField()
    answers = CharField()
    answers_md5 = CharField()
    question_type = IntegerField(default=1)
    status = BooleanField(default=False)

    class Meta:
        database = db


def select(qids, quetion_type):
    """
    查询数据
    :param qids:list 问题id
    :param quetion_type: int 问题类型1:判断，2：单选，3：多选
    :return: list
    """
    tmp = Answer.select(Answer.qid, Answer.aid).where(
        (Answer.question_type.in_(quetion_type)) & (Answer.qid.in_(qids)))
    return [x.__data__ for x in tmp]


def update_or_insert(data):
    """
    更新或者插入数据
    :param data: [{'qid': "1604631474830017", 'aid': "xleofzasdfdsafsadfyk"},
                {'qid': "1604631474830002", 'aid': "gtemqmjg"}]
    :return: int
    """
    return Answer.replace_many(data).execute()


if __name__ == '__main__':
    db.connect()
    db.create_tables([Answer])
    # data = [{'qid': "1604631474830017", 'aid': "xleofzasdfdsafsadfyk"}, {'qid': "1604631474830002", 'aid': "gtemqmjg"},
    #         {'qid': "1604630370569492", 'aid': "zuijrywu"}, {'qid': "1604630370569484", 'aid': "smsmflra"},
    #         {'qid': "1604630370sdafsa569498", 'aid': "lwjydfsx"}, {'qid': "1604630370569461", 'aid': "pobmxopm"},
    #         {'qid': "1604630370569452", 'aid': "agmzfcfp", 'question_type': 2},
    #         {'qid': "160463037dftgdf0569452", 'aid': "aaaaaa", 'question_type': 1}]
    # a = Answer.replace_many(data).execute()
    # model.insert(**{'qid': "1604630370569452", 'aid': "agmzfcfp"})
    # model.insert_many()
    # tmp = Answer.select(Answer.qid, Answer.aid).where(
    #     (Answer.question_type == 1) & (Answer.qid.in_(["1604631474830017", "1604630370569498"])))
    # data = select(["1604631474830017", "1604630370569498"], 1)
    pass
