import re
import requests
from loguru import logger
import json

re_title = re.compile(r'\d+、')
re_answer = re.compile(r'正确答案：(\S+)')
re_option = re.compile(r'\w+\. (.*)')

answer_to_option = {
    "A": 0,
    "B": 1,
    "C": 2,
    "D": 3,
    "E": 4,
    "F": 5,
    "G": 6,
    "H": 7
}


def read_data():
    tmp = {
        'title': '',
        'answers': '',
        'question_type': '',
    }
    options = []
    with open('data.txt', 'r', encoding='utf-8') as f:
        for txt in f.readlines():
            # 如果没有数据
            if not txt:
                break
            data = txt.strip()
            # 是否为标题
            if re_title.match(data):
                tmp['title'] = data
            # 是否为答案
            elif re_answer.match(data):
                answers = re_answer.findall(data)[0]
                tmp['answers'] = find_answer(answers, options)
                tmp['question_type'] = '单选题' if len(answers) == 1 else '多选题'
                yield tmp
                tmp = {}
                options = []
            elif re_option.match(data):
                options.append(parse_answer(data))
            # yield txt.strip()


def parse_question_type():
    pass


def find_answer(answer: str, options: list) -> str:
    answer_list = []
    for i in answer:
        answer_list.append(options[answer_to_option[i]])
    return '$'.join(answer_list)


def parse_answer(answer):
    return re_option.findall(answer)[0].strip()


def update_answer(data):
    resp = requests.post('https://school.moxiaoying.top/api/v1/answer/create', data=json.dumps(data))
    # resp = requests.post('http://0.0.0.0:8000/api/v1/answer/create', data=data)
    if resp.status_code != 200:
        logger.error(f'服务器保存异常: {data["title"]}')
        return
    resp = resp.json()
    if resp['code'] == 422:
        logger.error(f'更新答案失败: {data["title"]}')
    else:
        logger.success(f'更新答案成功: {data["title"]}')


if __name__ == '__main__':
    for answer in read_data():
        update_answer(answer)
        # print(answer)
        # break
