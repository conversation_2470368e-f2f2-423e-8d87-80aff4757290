from peewee import Model, <PERSON><PERSON><PERSON><PERSON>, IntegerField, BooleanField, SqliteDatabase
from answer import Answer as AnswerDb
from hashlib import md5
from unicodedata import normalize
from loguru import logger

db = SqliteDatabase('data/answer.db')


class Answer(Model):
    qid = <PERSON>r<PERSON><PERSON>(primary_key=True)
    title = CharField()
    answers = CharField()
    aid = CharField()
    question_type = IntegerField(default=1)
    status = BooleanField(default=False)

    class Meta:
        database = db


def my_md5(word):
    return md5(word.encode('utf-8')).hexdigest()


if __name__ == '__main__':
    for answer in Answer.select():
        data = {'qid': answer.qid,
                'answers_md5': '$'.join([my_md5(x) for x in answer.answers.split('$')]),
                'question_type': answer.question_type,
                'title': normalize('NFKC', answer.title),
                'answers': answer.answers,
                'status': True
                }
        item = AnswerDb.get_by_id(answer.qid)
        if not item.answers:
            item.answers = answer.answers
            item.answers_md5 = '$'.join([my_md5(x) for x in answer.answers.split('$')])
            item.save()
            # AnswerDb.replace(data).execute()
            logger.success(f"{item.title}修改成功")
        logger.info(f"{item.title} 不需要修改")
        pass
