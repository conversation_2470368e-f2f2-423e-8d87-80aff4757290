import time
from hashlib import md5
import re
from urllib.parse import quote
from unicodedata import normalize

import requests
from bs4 import BeautifulSoup
import datetime

from answer import Answer as AnswerDB
from loguru import logger


class BaseSpider(object):
    def __init__(self, *args, **kwargs):
        self.session = requests.session()
        self.headers = {
            'Host': '**************',
            'Cache-Control': 'max-age=0',
            'Upgrade-Insecure-Requests': '1',
            'Origin': 'http://**************',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.67 Safari/537.36 Edg/87.0.664.47',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Referer': 'http://**************/exam/user/exam/into',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            # 'Cookie': 'JSESSIONID=21C02986F6C530C9555D20E0D0BE6D9B; user_info_status_1e441353808e812e9a659b5f64553fc1f41d881ca290f8a4=1; user_info_status_ec21f1920f5b6eca45c7871e072b7a97f41d881ca290f8a4=1; user_info_status_e2e1f12aad418ee0d8bd456a99169c7cf41d881ca290f8a4=1; user_info_status_e2e1f12aad418ee019dc694eab1f03b4f41d881ca290f8a4=1',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        self.base_url = 'http://**************'

    def get_html(self, url):
        resp = self.session.get(url, headers=self.headers)
        soup = BeautifulSoup(resp.text, 'lxml')
        return soup

    @staticmethod
    def get_html_by_file(filename):
        with open(filename, mode='r', encoding='utf-8') as f:
            soup = BeautifulSoup(f.read(), 'lxml')
        return soup

    @staticmethod
    def get_soup(resp):
        return BeautifulSoup(resp.text, 'lxml')

    @staticmethod
    def save_to_html(filename, text):
        with open(filename, 'w+', encoding='utf-8') as f:
            f.write(text)
        logger.success(f'{filename} 保存成功')

    @staticmethod
    def md5(word):
        return md5(word.encode('utf-8')).hexdigest()


class Login(BaseSpider):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.idnumber = None
        self.name = None
        self.area = None
        self.access = None

    def login(self, idnumber, name, area='yongchuan'):
        self.idnumber = idnumber
        self.name = name
        self.area = area
        soup = self.get_html(f'{self.base_url}/exam/user/login?path={area}')
        area_id = soup.select_one('input[name="area_id"]').get('value')
        resp = self.session.post(f'{self.base_url}/exam/user/user_login', data={
            "idnumber": idnumber,
            "name": name,
            "area_id": area_id
        })
        logger.success(f'{name}--登陆成功')
        soup = self.get_soup(resp)
        self.access = soup.select_one('input[name="access"]').get('value')
        return soup

    pass


class Submit(Login):
    title_pattern = re.compile('\d+\.(.*)')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.answer = Answer()

    def get_question_resp(self, soup: BeautifulSoup) -> requests.Response:
        """
        提交数据，进入考试页面
        :param soup:
        :return:
        """
        logger.info('正在进入考试页面')
        plan_id = soup.select_one('.layui-btn-danger').get('href').split('\'')[1]
        post_data = {
            'access': soup.select_one('#intoForm input[name="access"]').get('value'),
            'plan_id': plan_id,
            'client_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        resp = self.session.post(f'{self.base_url}/exam/user/exam/into', data=post_data)
        return resp

    def get_questions(self, soup: BeautifulSoup) -> list:
        """
        获取问题id，选项id，问题类型
        :return: list 答题时问题(qid),对应选项(aid),问题类型：question_type
        """
        str2num = {
            '判断题': 1,
            '单选题': 2,
            '多选题': 3,
        }
        questions = []
        for items in soup.select('.qlist'):
            question_id = items.select_one('.item > input').get('name')
            answer_ids = {}
            for answer in items.select('.item'):
                answer_text = normalize('NFKC', answer.text.strip())
                answer_ids[answer_text] = answer.select_one('input').get('value')
                answer_ids[self.md5(answer_text)] = answer.select_one('input').get('value')
            question_type = str2num.get(items.select_one('span.layui-bg-blue').text)
            title = self.title_pattern.findall(items.select_one('span').nextSibling)[0].strip()
            questions.append(
                {'title': title,
                 'qid': question_id,
                 'aid': answer_ids,
                 'question_type': question_type,
                 })
        return questions

    def get_answer_params(self, questions: list) -> str:
        """
        构造提交时所需得answer参数
        :param questions:
        :return:
        """
        data_list = '['
        for question in questions:
            answer = self.answer.get_answer(question)
            aid = ''
            if answer.get("answers"):
                logger.success(f'题目：{question.get("title")}\t答案：{answer.get("answers")}')
                try:
                    aid = ','.join([question['aid'][answer_md5] for answer_md5 in answer['answers_md5'].split('$')])
                except Exception:
                    pass
            else:
                logger.error(f'题目：{question.get("title")}\t答案：{answer.get("answers")}')
            # data_list.append({
            #     'id': question['qid'],
            #     'aid': aid
            # })
            data_list += f'{{id:\"{question["qid"]}\",aid:\"{aid}\"}},'
        data_list = data_list[:-1] + ']'
        return data_list

    def submit_answer(self, soup: BeautifulSoup) -> requests.Response:
        logger.info('开始提交数据')
        questions = self.get_questions(soup)
        answer = self.get_answer_params(questions)
        post_data = {
            'access': soup.select_one('#submitForm input[name="access"]').get('value'),
            'plan_id': soup.select_one('#submitForm input[name="plan_id"]').get('value'),
            'exam_record_id': soup.select_one('#submitForm input[name="exam_record_id"]').get('value'),
            'answers': str(answer),
        }
        self.session.cookies.update({
            f'user_info_status_{post_data["access"]}': '1'
        })
        post_data = f'access={post_data["access"]}&plan_id={post_data["plan_id"]}&exam_record_id={post_data["exam_record_id"]}&answers={quote(post_data["answers"])}'
        # jar = requests.cookies.RequestsCookieJar()
        # jar.set(f'user_info_status_{post_data["access"]}', '1')

        resp = self.session.post(f'{self.base_url}/exam/user/exam/submit', post_data, headers=self.headers)
        logger.success('答案提交完成')
        return resp

    @staticmethod
    def parse_answer(soup: BeautifulSoup) -> list:
        """
        提交答案后，根据提交答案soup解析出答案
        :param soup: 答案页面soup
        :return: [['正确'],['错误']]
        """
        items = soup.select('div[style="margin-left: 52px;line-height:25px;margin-top: 2px;"]')
        answer_list = []
        for item in items:
            answers = [x.text.strip() for x in item.select('span[style="color: #5FB878;font-weight: bolder;"]')]
            answer_list.append(answers)
        return answer_list

    @staticmethod
    def parse_answer_by_record(soup: BeautifulSoup):
        items = soup.select('.R,.W')
        answer_list = []
        for item in items:
            answers = [x.text.strip() for x in item.select('span[style="color: #5FB878;font-weight: bolder;"]')]
            answer_list.append(answers)
        return answer_list

    def get_record_soup(self):
        url = f'{self.base_url}/exam/user/exam_record/list?access={self.access}&idnumber={self.idnumber}'
        resp = self.session.get(url)
        soup = self.get_soup(resp)
        item_id = re.findall(",'(\d+)", soup.select_one('a').get('href'))[0]
        item_url = f'{self.base_url}/exam/user/exam_record/view?access={self.access}&id={item_id}'
        return self.get_soup(self.session.get(item_url))
        pass


class Answer(BaseSpider):
    str_dict = {
        'A': 0,
        'B': 1,
        'C': 2,
        'D': 3,
        'E': 4,
        'F': 5,
        'G': 6,
    }
    db = AnswerDB
    answer_pattern = re.compile('[A-G]、(.*?)<')
    true_pattern = re.compile('([A-Z]+)')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def get_answer_by_url(self, question: dict):
        # question_title = 'B . 国家为了公共利益的需要，可以依照法律规定对土地实行征用并给予补偿'
        url = "https://gongxukemu.cn/search.html"
        payload = {
            'QuestionTitle': question['title']
        }
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        try:
            response = requests.request("POST", url, headers=headers, data=payload, timeout=5)
            soup = self.get_soup(response)
            item = soup.select_one('.span_1_of_b')
            answers = self.answer_pattern.findall(str(item.select('h3')[-3]))
            true_answers = self.true_pattern.findall(item.select_one('.ture-answer').text)[0]

            answer_texts = [answers[self.str_dict[answer]]
                            for answer in true_answers]
            # answer_ids = [question['aid'][answer_text] for answer_text in answer_texts]
            answer = {
                'title': question.get('title'),
                'qid': question.get('qid'),
                'answers_md5': '$'.join([self.md5(answer_text) for answer_text in answer_texts]),
                'answers': '$'.join(answer_texts),
                'question_type': question.get('question_type'),
            }
            if question['question_type'] == 3 and len(answer_texts) >= 2:
                return answer
            elif question['question_type'] < 3 and len(answer_texts) < 2:
                return answer
            else:
                logger.error(f"大爷的，答案不匹配{question['title']}")
                return {}
        except Exception as e:
            logger.error(f"{e}---{question['title']}")
            return {}

    def get_answer_by_db(self, question: dict):
        data = self.db.get_or_none(self.db.qid == question.get('qid'),
                                   self.db.question_type == question.get('question_type'))
        return data.__data__ if data else {}
        pass

    def get_answer(self, question: dict) -> dict:
        return self.get_answer_by_db(question) or self.get_answer_by_url(question)
        # return self.get_answer_by_db(question)

    def collect_answer(self, questions: list):
        for question in questions:
            answer = self.get_answer_by_url(question)
            if not answer:
                continue
            try:
                # self.db.insert(answer).execute()
                self.db.insert(title=answer['title'], answers=answer['answers'],
                               question_type=answer['question_type']).execute()
                logger.success(answer)
            except Exception as e:
                logger.error(f"{e}---{question['title']}已存在")
            time.sleep(0.1)
        pass

    def update_answer(self, question_list, answer_list):
        if len(question_list) != len(answer_list):
            raise Exception('问题和答案数量不匹配')
        for question, answers in zip(question_list, answer_list):
            qid = question.get('qid')
            question_type = question.get('question_type')
            # try:
            #     answers_md5 = ','.join([self.md5(x) for x in answers])
            # except Exception as e:
            #     print(e)
            #     aids = ''
            answer = {'qid': qid,
                      'answers_md5': '$'.join([self.md5(x) for x in answers]),
                      'question_type': question_type,
                      'title': question.get('title'),
                      'answers': '$'.join(answers),
                      'status': True
                      }
            logger.info(answer)
            self.db.replace(answer).execute()


def update_answer():
    submit = Submit()
    question_list = submit.get_questions(submit.get_html_by_file('data/into.html'))
    answer_list = submit.parse_answer_by_record(submit.get_html_by_file('data/record.html'))
    submit.answer.update_answer(question_list, answer_list)


def run():
    submit = Submit()
    # data = {
    #     "idnumber": "500221199712210236",
    #     "name": "张钦裕",
    #     "area": 'changshou'
    # }
    data = {
        "idnumber": "500381199407098649",
        "name": "张雨",
        "area": 'yongchuan'
    }

    # 进入登录页面
    login_soup = submit.login(**data)
    # 进入考试页面
    into_resp = submit.get_question_resp(login_soup)
    question_soup = submit.get_soup(into_resp)
    # 休息几分钟
    # logger.info('休息五分钟再提交数据')
    # time.sleep(5 * 60)
    # 提交考试数据
    submit.submit_answer(question_soup)
    # 获取页面答案
    record_soup = submit.get_record_soup()
    # 将数据保存到data/into.html中
    submit.save_to_html('data/into.html', into_resp.text)
    # submit.save_to_html('data/record.html', record_soup)
    # 更新答案
    question_list = submit.get_questions(question_soup)
    answer_list = submit.parse_answer_by_record(record_soup)
    submit.answer.update_answer(question_list, answer_list)


def collect_answer():
    submit = Submit()
    data = {
        "idnumber": "500383199610191577",
        "name": "袁国帅",
        "area": 'yongchuan'
    }

    # 进入登录页面
    login_soup = submit.login(**data)
    # 进入考试页面
    for _ in range(50):
        logger.info(_)
        into_resp = submit.get_question_resp(login_soup)
        question_soup = submit.get_soup(into_resp)

        question_list = submit.get_questions(question_soup)
        submit.answer.collect_answer(question_list)
        time.sleep(3)


def test():
    submit = Submit()
    data = {
        "idnumber": "500383199610191577",
        "name": "袁国帅",
        "area": 'yongchuan'
    }

    # 进入登录页面
    login_soup = submit.login(**data)
    soup = submit.get_record_soup()
    # 进入考试页面
    into_resp = submit.get_question_resp(login_soup)
    question_soup = submit.get_soup(into_resp)

    question_list = submit.get_questions(question_soup)
    answer_list = submit.get_answer_params(question_list)
    print(answer_list)
    # # submit.answer.update_answer(question_list, answer_list)


if __name__ == '__main__':
    run()
    # answer = Answer()
    # answer.collect_answer()
"""
"vxznrvfy,qvhqaqdt,vwoyqrnr,rprjgfsa"
<input name="access" type="hidden" value="ec21f1920f5b6eca45c7871e072b7a97f41d881ca290f8a4"/>
<input name="plan_id" type="hidden" value="1604631474830404"/>
<input name="exam_record_id" type="hidden" value="1606568885284190"/>
""" \
"""
[{id:"1604631474830017",aid:"xleofzyk"}{id:"1604631474830002",aid:"gtemqmjg"}{id:"1604630370569323",aid:"cmlfqpqs"}{id:"1604630370569302",aid:"ydlnzyrj"}{id:"1604630370569389",aid:"gwhaibgt"}{id:"1604630370569392",aid:"fqqxroji"}{id:"1604630370569418",aid:"hfyqjufz"}{id:"1604630370569402",aid:"zodwwqmj,zodwwqmj"}
    """
