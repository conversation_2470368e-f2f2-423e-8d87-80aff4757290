<?xml version='1.0' encoding='utf8'?>
<nuitka-compilation-report nuitka_version="2.4.11" nuitka_commercial_version="not installed" completion="error exit message (1)" exit_message="Failed unexpectedly in Scons C backend compilation.">
  <scons_error_reports>
    <scons_error_report>
      <command>"C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\ccache.exe" "C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe" -o "__constants.o" -c -std=c11 -flto=24 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -shared -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_MODULE -IC:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\inline_copy\zlib -IC:\PROGRA~1\PYTHON~1\include -I. -IC:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\include -IC:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\static_src -IC:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\inline_copy\libbacktrace "__constants.c"</command>
      <stderr>
In file included from C:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\include/nuitka/exceptions.h:10,
                 from C:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\include/nuitka/prelude.h:466,
                 from __constants.c:2:
C:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\include/nuitka/constants.h:222:10: fatal error: __constants.h: No such file or directory
  222 | #include "__constants.h"
      |          ^~~~~~~~~~~~~~~
compilation terminated.
</stderr>
    </scons_error_report>
    <scons_error_report>
      <command>"C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\ccache.exe" "C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe" -o "MODULE~1.o" -c -std=c11 -flto=24 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -shared -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_MODULE -IC:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\inline_copy\zlib -IC:\PROGRA~1\PYTHON~1\include -I. -IC:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\include -IC:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\static_src -IC:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\inline_copy\libbacktrace "MODULE~1.c"</command>
      <stderr>
In file included from C:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\include/nuitka/exceptions.h:10,
                 from C:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\include/nuitka/prelude.h:466,
                 from MODULE~1.c:19:
C:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\include/nuitka/constants.h:222:10: fatal error: __constants.h: No such file or directory
  222 | #include "__constants.h"
      |          ^~~~~~~~~~~~~~~
compilation terminated.
</stderr>
    </scons_error_report>
    <scons_error_report>
      <command>"C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\ccache.exe" "C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe" -o "MODULE~2.o" -c -std=c11 -flto=24 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -shared -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_MODULE -IC:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\inline_copy\zlib -IC:\PROGRA~1\PYTHON~1\include -I. -IC:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\include -IC:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\static_src -IC:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\inline_copy\libbacktrace "MODULE~2.c"</command>
      <stderr>
In file included from C:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\include/nuitka/exceptions.h:10,
                 from C:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\include/nuitka/prelude.h:466,
                 from MODULE~2.c:19:
C:\Users\<USER>\AppData\Roaming\Python\PYTHON~1\SITE-P~1\nuitka\build\include/nuitka/constants.h:222:10: fatal error: __constants.h: No such file or directory
  222 | #include "__constants.h"
      |          ^~~~~~~~~~~~~~~
compilation terminated.
</stderr>
    </scons_error_report>
  </scons_error_reports>
  <module name="安全教育平台" kind="CompiledPythonPackage" usage="root_module" reason="Root module" source_path="${cwd}\安全教育平台\__init__.py">
    <optimization-time pass="1" time="0.00" micro_passes="3" />
    <optimization-time pass="2" time="0.00" micro_passes="1" />
    <module_usages>
      <module_usage name="os" finding="excluded" line="1" exclusion_reason="Not following into stdlib unless standalone or requested to follow into stdlib." />
      <module_usage name="安全教育平台.index" finding="relative" line="1" />
    </module_usages>
  </module>
  <module name="安全教育平台.index" kind="CompiledPythonModule" usage="root_module" reason="Root module" source_path="${cwd}\安全教育平台\index.py">
    <optimization-time pass="1" time="0.09" micro_passes="5" max_branch_merge="64" merged_total="6362" />
    <optimization-time pass="2" time="0.02" micro_passes="1" max_branch_merge="62" merged_total="1199" />
    <module_usages>
      <module_usage name="configparser" finding="excluded" line="1" exclusion_reason="Not following into stdlib unless standalone or requested to follow into stdlib." />
      <module_usage name="time" finding="absolute" line="2" />
      <module_usage name="requests" finding="not-found" line="3" />
      <module_usage name="json" finding="excluded" line="4" exclusion_reason="Not following into stdlib unless standalone or requested to follow into stdlib." />
      <module_usage name="re" finding="excluded" line="5" exclusion_reason="Not following into stdlib unless standalone or requested to follow into stdlib." />
      <module_usage name="random" finding="excluded" line="6" exclusion_reason="Not following into stdlib unless standalone or requested to follow into stdlib." />
      <module_usage name="functools" finding="excluded" line="7" exclusion_reason="Not following into stdlib unless standalone or requested to follow into stdlib." />
      <module_usage name="datetime" finding="excluded" line="8" exclusion_reason="Not following into stdlib unless standalone or requested to follow into stdlib." />
      <module_usage name="loguru" finding="not-found" line="9" />
      <module_usage name="bs4" finding="not-found" line="10" />
      <module_usage name="urllib3" finding="not-found" line="11" />
    </module_usages>
  </module>
  <performance>
    <memory_usage name="after_launch" value="40816640" />
    <memory_usage name="before_c_code_generation" value="43655168" />
    <memory_usage name="before_running_scons" value="46690304" />
  </performance>
  <data_composer blob_size="16148" total="634">
    <module_data filename="__bytecode.const" input_size="0" blob_name=".bytecode" blob_size="3" />
    <module_data filename="__constants.const" input_size="1959" blob_name="" blob_size="791" />
    <module_data filename="module.安全教育平台.const" input_size="560" blob_name="安全教育平台" blob_size="297" />
    <module_data filename="module.安全教育平台.index.const" input_size="20634" blob_name="安全教育平台.index" blob_size="14978" />
  </data_composer>
  <command_line>
    <option value="--module" />
    <option value="安全教育平台" />
    <option value="--include-package=安全教育平台" />
  </command_line>
  <plugins>
    <plugin name="anti-bloat" user_enabled="no" />
    <plugin name="eventlet" user_enabled="no" />
    <plugin name="gi" user_enabled="no" />
    <plugin name="implicit-imports" user_enabled="no" />
    <plugin name="options-nanny" user_enabled="no" />
    <plugin name="pkg-resources" user_enabled="no" />
    <plugin name="transformers" user_enabled="no" />
  </plugins>
  <distributions />
  <python python_exe="${sys.prefix}\python.exe" python_flavor="CPython Official" python_version="3.12.6" os_name="Windows" os_release="11" arch_name="x86_64" filesystem_encoding="utf-8">
    <search_path>
      <path value="${cwd}" />
      <path value="${sys.prefix}\DLLs" />
      <path value="${sys.prefix}\Lib" />
      <path value="${sys.prefix}" />
      <path value="~\AppData\Roaming\Python\Python312\site-packages" />
      <path value="${sys.prefix}\Lib\site-packages" />
    </search_path>
  </python>
  <output run_filename="${cwd}\安全教育平台.cp312-win_amd64.pyd" />
</nuitka-compilation-report>
