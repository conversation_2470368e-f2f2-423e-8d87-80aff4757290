import json
import re
import time

import requests
from loguru import logger
from datetime import datetime
import pandas as pd
from bs4 import BeautifulSoup


class Spider:
    def __init__(self, token='', cookies=''):
        self.session = requests.session()
        self.token = token or ''
        self.cookies = cookies or ""

        self.session.headers = {
            "Cookie": self.cookies,
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36 Edg/113.0.1774.42"}

    def _filter_page_data(self, pub_item):
        data_list = []
        if not pub_item['publish_info']:
            return []
        for item in json.loads(pub_item['publish_info'])['appmsgex']:
            if not item['is_deleted']:
                data_list.append({
                    'title': item['title'],
                    'link': item['link'],
                    'cover': item['cover'],
                    'create_time': datetime.fromtimestamp(item['create_time']),
                    'update_time': datetime.fromtimestamp(item['update_time'])
                })
        return data_list
        pass

    def get_page_list(self, page=1, page_size=10):
        data_list = []
        url = "https://mp.weixin.qq.com/cgi-bin/appmsgpublish"
        begin = (page - 1) * page_size
        querystring = {"sub": "list", "begin": begin, "count": page_size, "sub_action": "list_ex",
                       "token": self.token,
                       "lang": "zh_CN", "f": "json"}
        response = self.session.get(url, params=querystring).json()
        data = json.loads(response['publish_page'])
        for item in data['publish_list']:
            data_list.extend(self._filter_page_data(item))
        return data_list

    @staticmethod
    def re_name(title: list, content):
        res = re.findall(f"[{'|'.join(title)}]\s*[|｜/：]\s*([\u4e00-\u9fa5\s]+)", str(content))
        if res:
            return res[0]
        return ''

    def get_detail_data(self, link):
        resp = self.session.get(link).text
        text = BeautifulSoup(resp, 'lxml').select_one('.rich_media_content')
        data = {
            'editer': self.re_name(['编辑', '排版'], str(text)),
            'shenhe': self.re_name(['审核'], str(text)),
            'author': self.re_name(['撰稿', '供稿'], str(text)),
        }
        return data

    def find_text(self, url, text_list):
        resp = self.session.get(url)
        data = re.findall(f"{'|'.join(text_list)}", resp.text)
        if data:
            return True
        return False

    def run(self):
        data_list = []
        for page in range(1, 32):
            logger.info(f'正在爬取第{page}页数据')
            for page_data in self.get_page_list(page):
                logger.success(f'正在爬取《{page_data["title"]}》')
                if self.find_text(page_data['link'], ['薄熙来', '王立军', '孙政才', '邓恢林']):
                    logger.error(f'《{page_data["title"]}》\t发现目标')
                # data = self.get_detail_data(page_data['link'])
                # page_data.update(data)
                # data_list.append(page_data)
                pass
            time.sleep(0.1)
        # df = pd.DataFrame(data_list)
        # df.to_excel('data.xlsx', index=False)
        pass


if __name__ == '__main__':
    spider = Spider(token='595834145',
                    cookies='appmsglist_action_3012095857=card; appmsglist_action_3529370962=card; iip=0; pgv_pvid=2219503817; tvfe_boss_uuid=08a9e72a7fd10e24; RK=uJ258sMhSG; ptcz=87f3c390eaa8d2cc3deddcbe92d76af4d31d8c9758d27d3e0068d8754c25c6f8; sd_userid=9751659701264518; sd_cookie_crttime=1659701264518; pac_uid=1_768091671; ua_id=RmikDroTa9c3qvKEAAAAANi-547F7A_xOx1Zpc9kRGM=; wxuin=62776144825302; mm_lang=zh_CN; o_cookie=768091671; fqm_pvqid=07dfed65-b7fd-4a80-8fe8-3eb5ddf7005d; eas_sid=i1Q6P6b880r7d4w9A0s4w5P1O7; LW_sid=51h6i7D3F0X821S3x0V0I1I7Q9; LW_uid=41J6u7Z3B0B8E163J0P0J11871; ied_qq=o0768091671; qq_domain_video_guid_verify=2ebedd125d14df5a; _qpsvr_localtk=0.3137662696386845; skey=@IhEghnfWJ; uin=o768091671; rewardsn=; wxtokenkey=777; wwapp.vid=; wwapp.cst=; wwapp.deviceid=; _clck=3529370962|1|fca|0; uuid=14730643dd3a3dd2999779cf7c4159c4; rand_info=CAESICDQR/cvmfJ8X3TkmdjeYdWO4wmVVU9am0W7gvSyzlb7; slave_bizuin=3012095857; data_bizuin=3006094933; bizuin=3012095857; data_ticket=YFX+EfS5WHeL/SZa63cMJQ0bcJMLFrQQgCY1cPm6336vT4EqSwJBbCu2Yf++8pjF; slave_sid=TnozNEdidlBaWFRVU1Y0cEYyZjJLR1dGUkNGc2R4UVFnaThjaWl0cldNQTFOY21yeVJiQU5zSldkcVJyd1QzRjFXODZ0dUZXd0hOQmFPTFVVVW9QYm00M0hsQmhHZ0kzVVc4ZGpWTURrSDViUnBxYUd2RVZjSG54MjA4NTFmT1VjVGZHWkU1bGxhNWxWMUdp; slave_user=gh_3e8a1b3aa211; xid=161cf46b094974141c58cfd2306307bb; _clsk=1ipu57k|1686237242183|35|1|mp.weixin.qq.com/weheat-agent/payload/record')
    spider.run()
