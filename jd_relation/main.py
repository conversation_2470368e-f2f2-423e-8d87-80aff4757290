# !/usr/bin/env python
# -*- coding: utf-8 -*-
import time

import pandas as pd
from exception import logger
from login import JD<PERSON>ogin


def get_html(session, keyword):
    url = f"https://dd-search.jd.com/?terminal=pc&newjson=1&ver=2&zip=1&key={keyword}"
    resp = session.get(url, timeout=5)
    data_list = eval(resp.text)
    return data_list[:-1]


def save_to_df(df: pd.DataFrame, data_list: list, keyword) -> pd.DataFrame:
    if not data_list:
        return df.append({
            'name': keyword,
            'value': 0
        }, ignore_index=True)
    for data in data_list:
        item = {
            'name': data['key'],
            'value': data['qre']
        }
        df = df.append(item, ignore_index=True)
        logger.success(item)
    return df


if __name__ == '__main__':
    login = JDLogin()
    login.login_by_QRcode()
    session = login.sess
    session.headers.update({
        'authority': 'dd-search.jd.com',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36 Edg/92.0.902.55',
        'accept': '*/*',
        'referer': 'https://www.jd.com/',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
    })
    excel = pd.read_excel('data/words.xls')
    df = pd.DataFrame(columns=['name', 'value'])
    for value in excel.iloc[:, 0].values:
        data_list = get_html(session, value)
        df = save_to_df(df, data_list, value)
        time.sleep(0.5)
    df.to_excel('data/output.xls', header=['商品名称', '数量'])
