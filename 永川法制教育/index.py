import requests

session = requests.session()
session.headers[
    'user-agent'] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36 Edg/113.0.1774.57"


def login(username, password='123456'):
    url = "https://chongqingshi.zhifa315.com/member/postMemberLogin"

    payload = {
        "idcardAndThirdNo": "510281198103033434",
        "password": "123456",
        "autoLogin": True,
        "loginType": "idcardAndThirdNo-password",
        "appId": "62bd630e6701899164fb3f51"
    }
    response = session.post(url, json=payload).json()
    session.headers['authorization'] = f'Bearer {response["data"]["memberToken"]}'
    return session


def get_course_detail(course_id, app_id="62bd630e6701899164fb3f51"):
    url = "https://chongqingshi.zhifa315.com/mycourse/getMycourseDetail"
    payload = {
        "mycourseId": course_id,
        "docIndex": None,
        "docId": None,
        "appId": app_id
    }
    response = session.request("POST", url, json=payload).json()
    if response['status']['code'] == 0:
        return response['data']
    raise Exception('获取课程详情失败')
    pass


def main():
    login('510281198103033434')
    course_info = get_course_detail(course_id='650018bd059431d0c1da00d2', app_id="62bd630e6701899164fb3f51")
    doc_dict = course_info['mycourseInfo']['learned']
    last_doc_id = course_info['mycourseInfo']['lastDocId']
    update_course(doc_id=last_doc_id, last_time=doc_dict[last_doc_id]['lastTime'],
                  random_str=doc_dict[last_doc_id]['random'])

def init_course():


def update_course(doc_id, last_time, random_str, course_id='650018bd059431d0c1da00d2',
                  app_id="62bd630e6701899164fb3f51"):
    url = "https://chongqingshi.zhifa315.com/mycourse/postUpdateTime"
    payload = {
        "mycourseId": course_id,
        "docId": doc_id,
        "random": random_str,
        "updateNumber": 60,
        "lastTime": last_time,
        "type": "normal",
        "minuteNumber": 60,
        "appId": app_id
    }
    response = session.request("POST", url, json=payload).json()

    print(response['data']['learnedProgress'])


if __name__ == '__main__':
    main()
